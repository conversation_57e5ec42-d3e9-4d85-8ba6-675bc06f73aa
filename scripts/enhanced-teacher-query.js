const mysql = require('mysql2/promise');

async function getEnhancedTeacherData() {
    const connection = await mysql.createConnection({
        host: 'localhost',
        user: 'root',
        password: '',
        database: 'exam_prep_platform'
    });

    try {
        console.log('🔍 ENHANCED TEACHER DATA QUERY\n');

        const teacherId = 103; // CS teacher user ID

        console.log('📋 MAIN TEACHER QUERY:');
        console.log('=' .repeat(80));
        
        const mainQuery = `
            SELECT
                -- User Table Fields
                u.id as user_id, 
                u.username, 
                u.name, 
                u.full_name, 
                u.email, 
                u.role, 
                u.profile_image,
                u.subjects as user_subjects, 
                u.bio, 
                u.date_of_birth, 
                u.created_at, 
                u.last_login, 
                u.is_active,
                u.institution, 
                u.grade, 
                u.field_of_study, 
                u.preferred_subjects, 
                u.target_exams,
                
                -- Staff Table Fields
                s.id as staff_id, 
                s.employee_id, 
                s.designation, 
                s.department, 
                s.current_school,
                s.joining_date, 
                s.employment_type, 
                s.phone, 
                s.alternate_phone, 
                s.emergency_contact,
                s.address, 
                s.city, 
                s.state, 
                s.pincode, 
                s.gender, 
                s.current_salary, 
                s.probation_period_months,
                s.confirmation_date, 
                s.last_promotion_date, 
                s.performance_rating, 
                s.is_on_leave,
                s.office_location, 
                s.subjects_taught, 
                s.classes_handled, 
                s.total_experience_years,
                s.teaching_experience_years, 
                s.administrative_experience_years, 
                s.awards_received,
                s.publications, 
                s.research_papers, 
                s.conferences_attended, 
                s.training_programs,
                s.notes as staff_notes, 
                s.is_active as staff_active
            FROM users u
            LEFT JOIN staff s ON u.id = s.user_id
            WHERE u.id = ? AND u.role = 'teacher'
        `;

        console.log(mainQuery);
        console.log('\nExecuting main query...\n');

        const [teacherData] = await connection.execute(mainQuery, [teacherId]);
        
        if (teacherData.length === 0) {
            console.log('❌ No teacher found');
            return;
        }

        const teacher = teacherData[0];
        
        console.log('✅ MAIN TEACHER DATA RETRIEVED:');
        console.log('=' .repeat(80));
        console.log('USER TABLE DATA:');
        console.log(`- User ID: ${teacher.user_id}`);
        console.log(`- Username: ${teacher.username}`);
        console.log(`- Name: ${teacher.name}`);
        console.log(`- Full Name: ${teacher.full_name}`);
        console.log(`- Email: ${teacher.email}`);
        console.log(`- Role: ${teacher.role}`);
        console.log(`- Bio: ${teacher.bio || 'Not provided'}`);
        console.log(`- Date of Birth: ${teacher.date_of_birth || 'Not provided'}`);
        console.log(`- Last Login: ${teacher.last_login || 'Never'}`);
        console.log(`- Account Active: ${teacher.is_active}`);
        console.log(`- User Subjects: ${teacher.user_subjects || 'Not specified'}`);

        console.log('\nSTAFF TABLE DATA:');
        console.log(`- Staff ID: ${teacher.staff_id}`);
        console.log(`- Employee ID: ${teacher.employee_id || 'Not set'}`);
        console.log(`- Designation: ${teacher.designation || 'Not set'}`);
        console.log(`- Department: ${teacher.department || 'Not set'}`);
        console.log(`- Gender: ${teacher.gender || 'Not specified'}`);
        console.log(`- Phone: ${teacher.phone || 'Not provided'}`);
        console.log(`- Joining Date: ${teacher.joining_date || 'Not set'}`);
        console.log(`- Employment Type: ${teacher.employment_type || 'Not set'}`);
        console.log(`- Subjects Taught: ${teacher.subjects_taught || 'Not specified'}`);
        console.log(`- Classes Handled: ${teacher.classes_handled || 'Not specified'}`);
        console.log(`- Total Experience: ${teacher.total_experience_years || 0} years`);
        console.log(`- Teaching Experience: ${teacher.teaching_experience_years || 0} years`);
        console.log(`- Staff Notes: ${teacher.staff_notes || 'No notes'}`);

        // Educational Qualifications Query
        console.log('\n📚 EDUCATIONAL QUALIFICATIONS QUERY:');
        console.log('=' .repeat(80));
        
        const educationQuery = `
            SELECT 
                qualification_level, 
                qualification_name, 
                specialization, 
                institution_name,
                university_board, 
                total_marks_obtained, 
                total_marks_maximum, 
                percentage,
                grade, 
                cgpa, 
                completion_year, 
                subjects, 
                achievements, 
                thesis_title
            FROM staff_educational_qualifications
            WHERE staff_id = ?
            ORDER BY completion_year ASC
        `;

        console.log(educationQuery);
        console.log('\nExecuting education query...\n');

        const [educationData] = await connection.execute(educationQuery, [teacher.staff_id]);
        
        console.log(`✅ EDUCATIONAL QUALIFICATIONS (${educationData.length} records):`);
        console.log('=' .repeat(80));
        educationData.forEach((item, index) => {
            console.log(`${index + 1}. ${item.qualification_name} (${item.completion_year})`);
            console.log(`   Level: ${item.qualification_level}`);
            console.log(`   Institution: ${item.institution_name}`);
            console.log(`   Board/University: ${item.university_board}`);
            console.log(`   Specialization: ${item.specialization || 'Not specified'}`);
            console.log(`   Percentage: ${item.percentage}%`);
            console.log(`   Grade: ${item.grade || 'Not specified'}`);
            console.log(`   CGPA: ${item.cgpa || 'Not specified'}`);
            console.log(`   Total Marks: ${item.total_marks_obtained}/${item.total_marks_maximum}`);
            if (item.subjects) {
                const subjects = JSON.parse(item.subjects);
                console.log(`   Subjects: ${Object.keys(subjects).length} subjects`);
                Object.entries(subjects).forEach(([subject, marks]) => {
                    console.log(`     - ${subject}: ${marks.marks}/${marks.total}`);
                });
            }
            console.log(`   Achievements: ${item.achievements || 'None listed'}`);
            console.log(`   Thesis: ${item.thesis_title || 'Not applicable'}`);
            console.log('');
        });

        // Professional Experience Query
        console.log('\n💼 PROFESSIONAL EXPERIENCE QUERY:');
        console.log('=' .repeat(80));
        
        const experienceQuery = `
            SELECT 
                job_title, 
                organization_name, 
                organization_type, 
                start_date, 
                end_date,
                is_current, 
                total_duration_months, 
                job_description, 
                key_responsibilities,
                achievements, 
                skills_used, 
                salary_range, 
                performance_rating
            FROM staff_professional_experience
            WHERE staff_id = ?
            ORDER BY start_date ASC
        `;

        console.log(experienceQuery);
        console.log('\nExecuting experience query...\n');

        const [experienceData] = await connection.execute(experienceQuery, [teacher.staff_id]);
        
        console.log(`✅ PROFESSIONAL EXPERIENCE (${experienceData.length} records):`);
        console.log('=' .repeat(80));
        experienceData.forEach((item, index) => {
            const duration = item.end_date 
                ? `${new Date(item.start_date).toLocaleDateString('en-US', {month: 'short', year: 'numeric'})} - ${new Date(item.end_date).toLocaleDateString('en-US', {month: 'short', year: 'numeric'})}`
                : `${new Date(item.start_date).toLocaleDateString('en-US', {month: 'short', year: 'numeric'})} - Present`;
            
            console.log(`${index + 1}. ${item.job_title} ${item.is_current ? '(CURRENT)' : '(PREVIOUS)'}`);
            console.log(`   Organization: ${item.organization_name} (${item.organization_type})`);
            console.log(`   Duration: ${duration} (${item.total_duration_months} months)`);
            console.log(`   Description: ${item.job_description}`);
            console.log(`   Performance Rating: ${item.performance_rating}`);
            console.log(`   Salary Range: ${item.salary_range || 'Not disclosed'}`);
            
            if (item.key_responsibilities) {
                const responsibilities = JSON.parse(item.key_responsibilities);
                console.log(`   Key Responsibilities (${responsibilities.length}):`);
                responsibilities.forEach((resp, idx) => {
                    console.log(`     ${idx + 1}. ${resp}`);
                });
            }
            
            if (item.achievements) {
                const achievements = JSON.parse(item.achievements);
                console.log(`   Achievements (${achievements.length}):`);
                achievements.forEach((achievement, idx) => {
                    console.log(`     ${idx + 1}. ${achievement}`);
                });
            }
            
            if (item.skills_used) {
                const skills = JSON.parse(item.skills_used);
                console.log(`   Skills Used: ${skills.join(', ')}`);
            }
            console.log('');
        });

        // Certifications Query
        console.log('\n📜 CERTIFICATIONS QUERY:');
        console.log('=' .repeat(80));
        
        const certificationsQuery = `
            SELECT 
                certification_name, 
                certification_type, 
                issuing_organization,
                issue_date, 
                expiry_date, 
                is_lifetime, 
                certificate_id,
                verification_status, 
                description, 
                skills_covered
            FROM staff_certifications
            WHERE staff_id = ?
            ORDER BY issue_date DESC
        `;

        console.log(certificationsQuery);
        console.log('\nExecuting certifications query...\n');

        const [certificationsData] = await connection.execute(certificationsQuery, [teacher.staff_id]);
        
        console.log(`✅ CERTIFICATIONS (${certificationsData.length} records):`);
        console.log('=' .repeat(80));
        certificationsData.forEach((cert, index) => {
            console.log(`${index + 1}. ${cert.certification_name}`);
            console.log(`   Type: ${cert.certification_type}`);
            console.log(`   Issuer: ${cert.issuing_organization}`);
            console.log(`   Issue Date: ${cert.issue_date}`);
            console.log(`   Expiry: ${cert.is_lifetime ? 'Lifetime' : cert.expiry_date}`);
            console.log(`   Certificate ID: ${cert.certificate_id}`);
            console.log(`   Status: ${cert.verification_status}`);
            console.log(`   Description: ${cert.description || 'Not provided'}`);
            if (cert.skills_covered) {
                const skills = JSON.parse(cert.skills_covered);
                console.log(`   Skills Covered: ${skills.join(', ')}`);
            }
            console.log('');
        });

        // Skills Query
        console.log('\n🛠️ SKILLS QUERY:');
        console.log('=' .repeat(80));
        
        const skillsQuery = `
            SELECT 
                skill_category, 
                skill_name, 
                proficiency_level, 
                years_of_experience,
                is_certified, 
                last_used_date
            FROM staff_skills
            WHERE staff_id = ?
            ORDER BY skill_category, proficiency_level DESC
        `;

        console.log(skillsQuery);
        console.log('\nExecuting skills query...\n');

        const [skillsData] = await connection.execute(skillsQuery, [teacher.staff_id]);
        
        console.log(`✅ SKILLS (${skillsData.length} records):`);
        console.log('=' .repeat(80));
        
        const skillsByCategory = {};
        skillsData.forEach(skill => {
            if (!skillsByCategory[skill.skill_category]) {
                skillsByCategory[skill.skill_category] = [];
            }
            skillsByCategory[skill.skill_category].push(skill);
        });

        Object.entries(skillsByCategory).forEach(([category, skills]) => {
            console.log(`${category.toUpperCase()} (${skills.length} skills):`);
            skills.forEach(skill => {
                console.log(`  • ${skill.skill_name} (${skill.proficiency_level}) - ${skill.years_of_experience} years ${skill.is_certified ? '✓ Certified' : ''}`);
                console.log(`    Last used: ${skill.last_used_date || 'Not specified'}`);
            });
            console.log('');
        });

        console.log('\n🎯 COMPLETE ENHANCED TEACHER DATA SUMMARY:');
        console.log('=' .repeat(80));
        console.log(`Teacher: ${teacher.full_name || teacher.name}`);
        console.log(`Email: ${teacher.email}`);
        console.log(`Gender: ${teacher.gender}`);
        console.log(`Staff ID: ${teacher.staff_id}`);
        console.log(`Educational Qualifications: ${educationData.length} records`);
        console.log(`Professional Experience: ${experienceData.length} records`);
        console.log(`Certifications: ${certificationsData.length} records`);
        console.log(`Skills: ${skillsData.length} records across ${Object.keys(skillsByCategory).length} categories`);
        
        const previousExperience = experienceData.filter(exp => !exp.is_current);
        const currentExperience = experienceData.filter(exp => exp.is_current);
        console.log(`Previous Experience: ${previousExperience.length} positions`);
        console.log(`Current Position: ${currentExperience.length} position`);

        console.log('\n✅ ALL ENHANCED TEACHER DATA RETRIEVED SUCCESSFULLY!');

    } catch (error) {
        console.error('Error retrieving enhanced teacher data:', error);
    } finally {
        await connection.end();
    }
}

getEnhancedTeacherData();
