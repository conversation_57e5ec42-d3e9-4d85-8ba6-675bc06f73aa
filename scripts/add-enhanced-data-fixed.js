const mysql = require('mysql2/promise');

async function addEnhancedDataFixed() {
    const connection = await mysql.createConnection({
        host: 'localhost',
        user: 'root',
        password: '',
        database: 'exam_prep_platform'
    });

    try {
        console.log('🎯 ADDING ENHANCED DATA FOR DISPLAYED TEACHER (FIXED)\n');

        // Add enhanced data for User ID 19 (cs_teacher1, Staff ID 11)
        const staffId = 11;
        const teacherName = 'cs_teacher1';
        
        console.log(`Adding enhanced data for ${teacherName} (Staff ID: ${staffId})`);

        // 1. Add Educational Qualifications (with shorter qualification_level values)
        console.log('\n📚 Adding Educational Qualifications...');
        await connection.execute(`
            INSERT INTO staff_educational_qualifications (
                staff_id, qualification_level, qualification_name, specialization,
                institution_name, university_board, total_marks_obtained, total_marks_maximum,
                percentage, grade, completion_year, subjects, achievements
            ) VALUES 
            (?, '10th', 'Secondary School Certificate', 'Science', 'Government High School', 'Punjab School Education Board', 420, 500, 84.00, 'A', 2005, '{"Mathematics": {"marks": 85, "total": 100}, "Science": {"marks": 82, "total": 100}, "English": {"marks": 80, "total": 100}, "Hindi": {"marks": 88, "total": 100}, "Social Science": {"marks": 85, "total": 100}}', 'School rank 5, Science fair winner'),
            (?, '12th', 'Higher Secondary Certificate', 'Science (Non-Medical)', 'DAV College', 'Punjab School Education Board', 450, 500, 90.00, 'A+', 2007, '{"Mathematics": {"marks": 92, "total": 100}, "Physics": {"marks": 88, "total": 100}, "Chemistry": {"marks": 90, "total": 100}, "English": {"marks": 85, "total": 100}, "Computer Science": {"marks": 95, "total": 100}}', 'District rank 8, Computer Science topper'),
            (?, 'graduation', 'Bachelor of Computer Applications', 'Computer Science', 'Punjab University', 'Punjab University', 1650, 2000, 82.50, 'A', 2010, '{"Programming": {"marks": 88, "total": 100}, "Database": {"marks": 85, "total": 100}, "Web Development": {"marks": 90, "total": 100}, "Software Engineering": {"marks": 82, "total": 100}, "Data Structures": {"marks": 87, "total": 100}}', 'Best Project Award, Programming competition winner')
        `, [staffId, staffId, staffId]);

        // 2. Add Professional Experience
        console.log('💼 Adding Professional Experience...');
        await connection.execute(`
            INSERT INTO staff_professional_experience (
                staff_id, job_title, organization_name, organization_type, start_date, end_date,
                is_current, total_duration_months, job_description, key_responsibilities,
                achievements, skills_used, performance_rating, salary_range
            ) VALUES 
            (?, 'Junior Developer', 'Tech Solutions Pvt Ltd', 'company', '2010-07-01', '2012-06-30', 0, 24, 'Developed web applications and maintained existing software systems for small business clients.', '["Developed PHP web applications", "Maintained MySQL databases", "Created responsive web interfaces", "Debugged and fixed software issues", "Collaborated with senior developers"]', '["Successfully completed 12 client projects", "Reduced bug reports by 30%", "Learned advanced PHP frameworks", "Improved code quality through peer reviews"]', '["PHP", "MySQL", "HTML", "CSS", "JavaScript", "jQuery"]', 'good', '18000-25000'),
            (?, 'Computer Science Teacher', 'Government Senior Secondary School', 'school', '2012-07-01', NULL, 1, 150, 'Teaching Computer Science to students from Class 9 to 12, managing computer lab, and mentoring students in programming.', '["Teaching programming concepts to students", "Managing computer lab equipment", "Preparing lesson plans and assignments", "Conducting practical sessions", "Mentoring students in coding competitions", "Maintaining student records"]', '["100% pass rate in Computer Science", "Students won 3 district programming competitions", "Implemented new practical curriculum", "Reduced lab equipment downtime by 40%", "Trained 5 junior teachers", "Organized successful coding workshops"]', '["Java", "Python", "C++", "HTML", "CSS", "Database Management", "Teaching", "Lab Management"]', 'excellent', '35000-45000')
        `, [staffId, staffId]);

        // 3. Add Certifications
        console.log('📜 Adding Certifications...');
        await connection.execute(`
            INSERT INTO staff_certifications (
                staff_id, certification_name, certification_type, issuing_organization,
                issue_date, expiry_date, is_lifetime, certificate_id, verification_status,
                description, skills_covered
            ) VALUES 
            (?, 'B.Ed (Bachelor of Education)', 'teaching', 'Punjab University', '2015-06-30', NULL, 1, 'BED2015PU789', 'verified', 'Bachelor of Education degree focusing on teaching methodologies and educational psychology.', 'Educational Psychology, Teaching Methods, Classroom Management, Assessment Techniques'),
            (?, 'Oracle Java SE 11 Developer', 'technical', 'Oracle Corporation', '2018-03-15', '2028-03-15', 0, 'OCP11DEV456', 'verified', 'Oracle Certified Professional Java SE 11 Developer certification.', 'Java Programming, Object-Oriented Programming, Exception Handling, Collections Framework')
        `, [staffId, staffId]);

        // 4. Add Skills
        console.log('🛠️ Adding Skills...');
        await connection.execute(`
            INSERT INTO staff_skills (
                staff_id, skill_category, skill_name, proficiency_level,
                years_of_experience, is_certified, last_used_date
            ) VALUES 
            (?, 'technical', 'Java Programming', 'advanced', 10.0, 1, '2024-05-30'),
            (?, 'technical', 'Python Programming', 'intermediate', 6.0, 0, '2024-05-30'),
            (?, 'technical', 'C++ Programming', 'advanced', 8.0, 0, '2024-05-30'),
            (?, 'technical', 'MySQL Database', 'advanced', 10.0, 0, '2024-05-30'),
            (?, 'technical', 'Web Development', 'intermediate', 5.0, 0, '2024-05-30'),
            (?, 'teaching', 'Classroom Management', 'expert', 12.0, 1, '2024-05-30'),
            (?, 'teaching', 'Student Assessment', 'expert', 12.0, 1, '2024-05-30'),
            (?, 'teaching', 'Curriculum Development', 'advanced', 8.0, 1, '2024-05-30'),
            (?, 'teaching', 'Educational Technology', 'advanced', 6.0, 0, '2024-05-30'),
            (?, 'language', 'English', 'expert', 15.0, 0, '2024-05-30'),
            (?, 'language', 'Hindi', 'expert', 15.0, 0, '2024-05-30'),
            (?, 'language', 'Punjabi', 'expert', 15.0, 0, '2024-05-30'),
            (?, 'soft_skill', 'Communication', 'expert', 12.0, 0, '2024-05-30'),
            (?, 'soft_skill', 'Problem Solving', 'expert', 10.0, 0, '2024-05-30'),
            (?, 'soft_skill', 'Team Leadership', 'advanced', 6.0, 0, '2024-05-30')
        `, [staffId, staffId, staffId, staffId, staffId, staffId, staffId, staffId, staffId, staffId, staffId, staffId, staffId, staffId, staffId]);

        console.log('\n✅ ENHANCED DATA ADDED SUCCESSFULLY!');
        
        // Verify the data was added
        console.log('\n🔍 VERIFICATION:');
        const [education] = await connection.execute(`SELECT COUNT(*) as count FROM staff_educational_qualifications WHERE staff_id = ?`, [staffId]);
        const [experience] = await connection.execute(`SELECT COUNT(*) as count FROM staff_professional_experience WHERE staff_id = ?`, [staffId]);
        const [certifications] = await connection.execute(`SELECT COUNT(*) as count FROM staff_certifications WHERE staff_id = ?`, [staffId]);
        const [skills] = await connection.execute(`SELECT COUNT(*) as count FROM staff_skills WHERE staff_id = ?`, [staffId]);
        
        console.log(`Education records: ${education[0].count}`);
        console.log(`Experience records: ${experience[0].count}`);
        console.log(`Certification records: ${certifications[0].count}`);
        console.log(`Skill records: ${skills[0].count}`);
        console.log(`Total enhanced records: ${education[0].count + experience[0].count + certifications[0].count + skills[0].count}`);
        
        console.log('\n🎯 NOW YOU CAN TEST THE MODAL:');
        console.log(`✅ User ID 19 (cs_teacher1) now has enhanced data`);
        console.log(`✅ Click the eye icon for cs_teacher1 in the teacher management table`);
        console.log(`✅ The enhanced teacher modal should display all the data`);

    } catch (error) {
        console.error('Error:', error);
    } finally {
        await connection.end();
    }
}

addEnhancedDataFixed();
