const mysql = require('mysql2/promise');

async function testUpdatedAPI() {
    const connection = await mysql.createConnection({
        host: 'localhost',
        user: 'root',
        password: '',
        database: 'exam_prep_platform'
    });

    try {
        console.log('🧪 TESTING UPDATED API WITH CORRECTED QUERIES\n');

        // Get CS teacher staff ID
        const [userResult] = await connection.execute(
            'SELECT u.id as user_id, s.id as staff_id FROM users u JOIN staff s ON u.id = s.user_id WHERE u.email = ?',
            ['<EMAIL>']
        );

        const { staff_id: staffId } = userResult[0];
        console.log(`📋 Testing API for Staff ID: ${staffId}\n`);

        // Test Educational Qualifications Query
        console.log('🎓 TESTING EDUCATIONAL QUALIFICATIONS QUERY:');
        console.log('=' .repeat(60));
        
        const [educationData] = await connection.execute(`
            SELECT qualification_level, qualification_name, specialization, institution_name,
                   university_board, total_marks_obtained, total_marks_maximum, percentage,
                   grade, cgpa, completion_year, subjects, achievements, thesis_title
            FROM staff_educational_qualifications
            WHERE staff_id = ?
            ORDER BY completion_year ASC
        `, [staffId]);

        console.log(`Found ${educationData.length} educational qualifications:`);
        educationData.forEach((item, index) => {
            console.log(`${index + 1}. ${item.qualification_name} (${item.completion_year})`);
            console.log(`   Institution: ${item.institution_name}`);
            console.log(`   Percentage: ${item.percentage}%`);
            if (item.subjects) {
                const subjects = JSON.parse(item.subjects);
                console.log(`   Subjects: ${Object.keys(subjects).length} subjects`);
            }
            console.log('');
        });

        // Test Professional Experience Query
        console.log('💼 TESTING PROFESSIONAL EXPERIENCE QUERY:');
        console.log('=' .repeat(60));
        
        const [experienceData] = await connection.execute(`
            SELECT job_title, organization_name, organization_type, start_date, end_date,
                   is_current, total_duration_months, job_description, key_responsibilities,
                   achievements, skills_used, salary_range, performance_rating
            FROM staff_professional_experience
            WHERE staff_id = ?
            ORDER BY start_date ASC
        `, [staffId]);

        console.log(`Found ${experienceData.length} professional experiences:`);
        experienceData.forEach((item, index) => {
            const duration = item.end_date 
                ? `${new Date(item.start_date).toLocaleDateString('en-US', {month: 'short', year: 'numeric'})} - ${new Date(item.end_date).toLocaleDateString('en-US', {month: 'short', year: 'numeric'})}`
                : `${new Date(item.start_date).toLocaleDateString('en-US', {month: 'short', year: 'numeric'})} - Present`;
            
            console.log(`${index + 1}. ${item.job_title} ${item.is_current ? '(CURRENT)' : ''}`);
            console.log(`   Organization: ${item.organization_name}`);
            console.log(`   Duration: ${duration}`);
            console.log(`   Performance: ${item.performance_rating}`);
            
            if (item.key_responsibilities) {
                const responsibilities = JSON.parse(item.key_responsibilities);
                console.log(`   Responsibilities: ${responsibilities.length} items`);
            }
            
            if (item.achievements) {
                const achievements = JSON.parse(item.achievements);
                console.log(`   Achievements: ${achievements.length} items`);
            }
            
            if (item.skills_used) {
                const skills = JSON.parse(item.skills_used);
                console.log(`   Skills: ${skills.length} skills`);
            }
            console.log('');
        });

        // Test Certifications Query
        console.log('📜 TESTING CERTIFICATIONS QUERY:');
        console.log('=' .repeat(60));
        
        const [certificationsData] = await connection.execute(`
            SELECT certification_name, certification_type, issuing_organization,
                   issue_date, expiry_date, is_lifetime, certificate_id,
                   verification_status, description, skills_covered
            FROM staff_certifications
            WHERE staff_id = ?
            ORDER BY issue_date DESC
        `, [staffId]);

        console.log(`Found ${certificationsData.length} certifications:`);
        certificationsData.forEach((cert, index) => {
            console.log(`${index + 1}. ${cert.certification_name}`);
            console.log(`   Issuer: ${cert.issuing_organization}`);
            console.log(`   Status: ${cert.verification_status}`);
            if (cert.skills_covered) {
                const skills = JSON.parse(cert.skills_covered);
                console.log(`   Skills Covered: ${skills.length} skills`);
            }
            console.log('');
        });

        // Test Skills Query
        console.log('🛠️ TESTING SKILLS QUERY:');
        console.log('=' .repeat(60));
        
        const [skillsData] = await connection.execute(`
            SELECT skill_category, skill_name, proficiency_level, years_of_experience,
                   is_certified, last_used_date
            FROM staff_skills
            WHERE staff_id = ?
            ORDER BY skill_category, proficiency_level DESC
        `, [staffId]);

        const skillsByCategory = {};
        skillsData.forEach(skill => {
            if (!skillsByCategory[skill.skill_category]) {
                skillsByCategory[skill.skill_category] = [];
            }
            skillsByCategory[skill.skill_category].push({
                name: skill.skill_name,
                proficiency: skill.proficiency_level,
                experience: skill.years_of_experience,
                certified: skill.is_certified
            });
        });

        console.log(`Found ${skillsData.length} skills in ${Object.keys(skillsByCategory).length} categories:`);
        Object.entries(skillsByCategory).forEach(([category, skills]) => {
            console.log(`\n${category.toUpperCase()} (${skills.length} skills):`);
            skills.forEach(skill => {
                console.log(`   • ${skill.name} (${skill.proficiency}) - ${skill.experience} years ${skill.certified ? '✓ Certified' : ''}`);
            });
        });

        console.log('\n✅ API DATA STRUCTURE TEST COMPLETED SUCCESSFULLY!');
        console.log('\n📊 SUMMARY:');
        console.log(`- Educational Qualifications: ${educationData.length}`);
        console.log(`- Professional Experience: ${experienceData.length}`);
        console.log(`- Certifications: ${certificationsData.length}`);
        console.log(`- Skills: ${skillsData.length}`);
        console.log(`- Skill Categories: ${Object.keys(skillsByCategory).length}`);

    } catch (error) {
        console.error('Error testing updated API:', error);
    } finally {
        await connection.end();
    }
}

testUpdatedAPI();
