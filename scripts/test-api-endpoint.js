const http = require('http');

function testAPIEndpoint() {
    const options = {
        hostname: 'localhost',
        port: 3018,
        path: '/principal/api/teacher/profile-enhanced?teacher_id=103',
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'Cookie': 'connect.sid=s%3A...' // You might need to add session cookie
        }
    };

    const req = http.request(options, (res) => {
        console.log(`Status: ${res.statusCode}`);
        console.log(`Headers: ${JSON.stringify(res.headers)}`);
        
        let data = '';
        res.on('data', (chunk) => {
            data += chunk;
        });
        
        res.on('end', () => {
            try {
                const jsonData = JSON.parse(data);
                console.log('\n=== API RESPONSE ===');
                console.log('Success:', jsonData.success);
                if (jsonData.teacher) {
                    console.log('Teacher ID:', jsonData.teacher.id);
                    console.log('Teacher Name:', jsonData.teacher.name);
                    console.log('Education Timeline Length:', jsonData.teacher.educationTimeline ? jsonData.teacher.educationTimeline.length : 'undefined');
                    console.log('Experience Timeline Length:', jsonData.teacher.experienceTimeline ? jsonData.teacher.experienceTimeline.length : 'undefined');
                    
                    if (jsonData.teacher.experienceTimeline) {
                        console.log('\n=== EXPERIENCE TIMELINE FROM API ===');
                        jsonData.teacher.experienceTimeline.forEach((item, index) => {
                            console.log(`${index + 1}. ${item.title} - ${item.institution}`);
                            console.log(`   Duration: ${item.duration}`);
                            console.log(`   isCurrent: ${item.isCurrent}`);
                            console.log(`   Description: ${item.description}`);
                            console.log('---');
                        });
                    }
                } else {
                    console.log('No teacher data in response');
                }
                
                if (jsonData.message) {
                    console.log('Message:', jsonData.message);
                }
            } catch (e) {
                console.log('Raw response:', data);
                console.log('Parse error:', e.message);
            }
        });
    });

    req.on('error', (e) => {
        console.error(`Problem with request: ${e.message}`);
    });

    req.end();
}

console.log('Testing API endpoint...');
testAPIEndpoint();
