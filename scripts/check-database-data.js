const mysql = require('mysql2/promise');

async function checkDatabaseData() {
    const connection = await mysql.createConnection({
        host: 'localhost',
        user: 'root',
        password: '',
        database: 'exam_prep_platform'
    });

    try {
        console.log('🔍 CHECKING DATABASE DATA FOR CS TEACHER\n');

        // Check users table
        console.log('📋 CHECKING USERS TABLE:');
        const [users] = await connection.execute(`
            SELECT id, username, name, full_name, email, role 
            FROM users 
            WHERE role = 'teacher' AND id = 103
        `);
        console.log('Users found:', users.length);
        users.forEach(user => {
            console.log(`  User ID: ${user.id}, Name: ${user.name}, Email: ${user.email}`);
        });

        // Check staff table
        console.log('\n👥 CHECKING STAFF TABLE:');
        const [staff] = await connection.execute(`
            SELECT id, user_id, employee_id, designation, gender 
            FROM staff 
            WHERE user_id = 103
        `);
        console.log('Staff records found:', staff.length);
        staff.forEach(s => {
            console.log(`  Staff ID: ${s.id}, User ID: ${s.user_id}, Employee ID: ${s.employee_id}, Gender: ${s.gender}`);
        });

        if (staff.length === 0) {
            console.log('❌ NO STAFF RECORD FOUND - This is why staff_id is undefined!');
            return;
        }

        const staffId = staff[0].id;
        console.log(`\nUsing Staff ID: ${staffId} for related queries\n`);

        // Check educational qualifications
        console.log('📚 CHECKING EDUCATIONAL QUALIFICATIONS:');
        const [education] = await connection.execute(`
            SELECT id, qualification_name, completion_year, percentage, staff_id
            FROM staff_educational_qualifications 
            WHERE staff_id = ?
        `, [staffId]);
        console.log('Education records found:', education.length);
        education.forEach(edu => {
            console.log(`  ${edu.qualification_name} (${edu.completion_year}) - ${edu.percentage}%`);
        });

        if (education.length === 0) {
            console.log('❌ NO EDUCATION RECORDS FOUND - This is why education timeline is empty!');
        }

        // Check professional experience
        console.log('\n💼 CHECKING PROFESSIONAL EXPERIENCE:');
        const [experience] = await connection.execute(`
            SELECT id, job_title, organization_name, is_current, start_date, staff_id
            FROM staff_professional_experience 
            WHERE staff_id = ?
        `, [staffId]);
        console.log('Experience records found:', experience.length);
        experience.forEach(exp => {
            console.log(`  ${exp.job_title} at ${exp.organization_name} (${exp.is_current ? 'CURRENT' : 'PREVIOUS'})`);
        });

        if (experience.length === 0) {
            console.log('❌ NO EXPERIENCE RECORDS FOUND - This is why experience timeline is limited!');
        }

        // Check certifications
        console.log('\n📜 CHECKING CERTIFICATIONS:');
        const [certifications] = await connection.execute(`
            SELECT id, certification_name, certification_type, staff_id
            FROM staff_certifications 
            WHERE staff_id = ?
        `, [staffId]);
        console.log('Certification records found:', certifications.length);
        certifications.forEach(cert => {
            console.log(`  ${cert.certification_name} (${cert.certification_type})`);
        });

        // Check skills
        console.log('\n🛠️ CHECKING SKILLS:');
        const [skills] = await connection.execute(`
            SELECT id, skill_name, skill_category, proficiency_level, staff_id
            FROM staff_skills 
            WHERE staff_id = ?
        `, [staffId]);
        console.log('Skill records found:', skills.length);
        skills.forEach(skill => {
            console.log(`  ${skill.skill_name} (${skill.skill_category}) - ${skill.proficiency_level}`);
        });

        // Summary
        console.log('\n🎯 DATABASE DATA SUMMARY:');
        console.log('=' .repeat(60));
        console.log(`✅ User Record: ${users.length > 0 ? 'EXISTS' : 'MISSING'}`);
        console.log(`${staff.length > 0 ? '✅' : '❌'} Staff Record: ${staff.length > 0 ? 'EXISTS' : 'MISSING'}`);
        console.log(`${education.length > 0 ? '✅' : '❌'} Education Records: ${education.length} found`);
        console.log(`${experience.length > 0 ? '✅' : '❌'} Experience Records: ${experience.length} found`);
        console.log(`${certifications.length > 0 ? '✅' : '❌'} Certification Records: ${certifications.length} found`);
        console.log(`${skills.length > 0 ? '✅' : '❌'} Skill Records: ${skills.length} found`);

        if (staff.length === 0) {
            console.log('\n🚨 CRITICAL ISSUE: No staff record exists for user ID 103');
            console.log('This means the user exists but is not properly linked to staff data.');
            console.log('Need to create a staff record or fix the user-staff relationship.');
        } else if (education.length === 0 && experience.length === 0) {
            console.log('\n🚨 DATA ISSUE: Staff record exists but no enhanced profile data');
            console.log('The enhanced profile tables are empty for this teacher.');
            console.log('Need to populate the enhanced profile data tables.');
        } else {
            console.log('\n✅ Data structure looks good - API should work correctly');
        }

    } catch (error) {
        console.error('Error checking database data:', error);
    } finally {
        await connection.end();
    }
}

checkDatabaseData();
