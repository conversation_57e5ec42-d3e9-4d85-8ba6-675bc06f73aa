const mysql = require('mysql2/promise');

async function executeCompleteTeacherQueries() {
    const connection = await mysql.createConnection({
        host: 'localhost',
        user: 'root',
        password: '',
        database: 'exam_prep_platform'
    });

    try {
        console.log('🎯 COMPLETE TEACHER DATA QUERIES - ALL DATA INCLUDING PREVIOUS EXPERIENCE\n');

        const teacherId = 103;

        // QUERY 1: Main Teacher Information
        console.log('📋 QUERY 1: MAIN TEACHER INFORMATION');
        console.log('=' .repeat(80));
        
        const mainQuery = `
            SELECT 
                -- User Table Data
                u.id as user_id,
                u.username,
                u.name,
                u.full_name,
                u.email,
                u.role,
                u.profile_image,
                u.subjects as user_subjects,
                u.bio,
                u.date_of_birth,
                u.created_at,
                u.last_login,
                u.is_active,
                
                -- Staff Table Data
                s.id as staff_id,
                s.employee_id,
                s.designation,
                s.department,
                s.current_school,
                s.joining_date,
                s.employment_type,
                s.phone,
                s.alternate_phone,
                s.emergency_contact,
                s.address,
                s.city,
                s.state,
                s.pincode,
                s.gender,
                s.current_salary,
                s.subjects_taught,
                s.classes_handled,
                s.total_experience_years,
                s.teaching_experience_years,
                s.performance_rating,
                s.awards_received,
                s.notes as staff_notes,
                
                -- Calculated Fields
                FLOOR(DATEDIFF(CURDATE(), u.date_of_birth) / 365.25) as calculated_age,
                COALESCE(u.full_name, u.name, u.username) as display_name,
                CASE WHEN u.is_active = 1 THEN 'Active' ELSE 'Inactive' END as account_status
                
            FROM users u
            LEFT JOIN staff s ON u.id = s.user_id
            WHERE u.id = ? AND u.role = 'teacher'
        `;

        console.log(mainQuery);
        const [teacherData] = await connection.execute(mainQuery, [teacherId]);
        
        if (teacherData.length === 0) {
            console.log('❌ No teacher found');
            return;
        }

        const teacher = teacherData[0];
        console.log('\n✅ MAIN TEACHER DATA:');
        console.log(`Name: ${teacher.display_name}`);
        console.log(`Email: ${teacher.email}`);
        console.log(`Gender: ${teacher.gender}`);
        console.log(`Staff ID: ${teacher.staff_id}`);
        console.log(`Age: ${teacher.calculated_age} years`);

        // QUERY 2: Educational Qualifications (ALL RECORDS)
        console.log('\n📚 QUERY 2: ALL EDUCATIONAL QUALIFICATIONS');
        console.log('=' .repeat(80));
        
        const educationQuery = `
            SELECT 
                eq.id,
                eq.qualification_level,
                eq.qualification_name,
                eq.specialization,
                eq.institution_name,
                eq.university_board,
                eq.total_marks_obtained,
                eq.total_marks_maximum,
                eq.percentage,
                eq.grade,
                eq.cgpa,
                eq.completion_year,
                eq.subjects,
                eq.achievements,
                eq.thesis_title,
                eq.created_at
            FROM staff_educational_qualifications eq
            WHERE eq.staff_id = ?
            ORDER BY eq.completion_year ASC
        `;

        console.log(educationQuery);
        const [educationData] = await connection.execute(educationQuery, [teacher.staff_id]);
        
        console.log(`\n✅ EDUCATIONAL QUALIFICATIONS (${educationData.length} records):`);
        educationData.forEach((edu, index) => {
            console.log(`${index + 1}. ${edu.qualification_name} (${edu.completion_year}) - ${edu.percentage}%`);
            console.log(`   Institution: ${edu.institution_name}`);
            console.log(`   Specialization: ${edu.specialization || 'Not specified'}`);
            console.log(`   Grade: ${edu.grade || 'Not specified'} | CGPA: ${edu.cgpa || 'Not specified'}`);
            if (edu.subjects) {
                const subjects = JSON.parse(edu.subjects);
                console.log(`   Subjects: ${Object.keys(subjects).length} subjects`);
            }
            console.log(`   Achievements: ${edu.achievements || 'None listed'}`);
            console.log('');
        });

        // QUERY 3: Professional Experience (ALL RECORDS - CURRENT AND PREVIOUS)
        console.log('\n💼 QUERY 3: ALL PROFESSIONAL EXPERIENCE (CURRENT + PREVIOUS)');
        console.log('=' .repeat(80));
        
        const experienceQuery = `
            SELECT 
                pe.id,
                pe.job_title,
                pe.organization_name,
                pe.organization_type,
                pe.start_date,
                pe.end_date,
                pe.is_current,
                pe.total_duration_months,
                pe.job_description,
                pe.key_responsibilities,
                pe.achievements,
                pe.skills_used,
                pe.salary_range,
                pe.performance_rating,
                pe.created_at,
                CASE WHEN pe.is_current = 1 THEN 'CURRENT' ELSE 'PREVIOUS' END as position_status
            FROM staff_professional_experience pe
            WHERE pe.staff_id = ?
            ORDER BY pe.start_date ASC
        `;

        console.log(experienceQuery);
        const [experienceData] = await connection.execute(experienceQuery, [teacher.staff_id]);
        
        console.log(`\n✅ PROFESSIONAL EXPERIENCE (${experienceData.length} records):`);
        
        const previousExperience = experienceData.filter(exp => exp.is_current === 0);
        const currentExperience = experienceData.filter(exp => exp.is_current === 1);
        
        console.log(`\n🔍 BREAKDOWN:`);
        console.log(`- Previous Experience: ${previousExperience.length} positions`);
        console.log(`- Current Position: ${currentExperience.length} position`);
        
        console.log(`\n📋 ALL EXPERIENCE RECORDS:`);
        experienceData.forEach((exp, index) => {
            const duration = exp.end_date 
                ? `${new Date(exp.start_date).toLocaleDateString()} - ${new Date(exp.end_date).toLocaleDateString()}`
                : `${new Date(exp.start_date).toLocaleDateString()} - Present`;
            
            console.log(`${index + 1}. ${exp.job_title} (${exp.position_status})`);
            console.log(`   Organization: ${exp.organization_name} (${exp.organization_type})`);
            console.log(`   Duration: ${duration} (${exp.total_duration_months} months)`);
            console.log(`   Description: ${exp.job_description}`);
            console.log(`   Performance: ${exp.performance_rating} | Salary: ${exp.salary_range}`);
            
            if (exp.key_responsibilities) {
                const responsibilities = JSON.parse(exp.key_responsibilities);
                console.log(`   Responsibilities: ${responsibilities.length} items`);
            }
            
            if (exp.achievements) {
                const achievements = JSON.parse(exp.achievements);
                console.log(`   Achievements: ${achievements.length} items`);
            }
            
            if (exp.skills_used) {
                const skills = JSON.parse(exp.skills_used);
                console.log(`   Skills Used: ${skills.length} skills`);
            }
            console.log('');
        });

        // QUERY 4: Certifications
        console.log('\n📜 QUERY 4: ALL CERTIFICATIONS');
        console.log('=' .repeat(80));
        
        const certificationsQuery = `
            SELECT 
                c.id,
                c.certification_name,
                c.certification_type,
                c.issuing_organization,
                c.issue_date,
                c.expiry_date,
                c.is_lifetime,
                c.certificate_id,
                c.verification_status,
                c.description,
                c.skills_covered
            FROM staff_certifications c
            WHERE c.staff_id = ?
            ORDER BY c.issue_date DESC
        `;

        console.log(certificationsQuery);
        const [certificationsData] = await connection.execute(certificationsQuery, [teacher.staff_id]);
        
        console.log(`\n✅ CERTIFICATIONS (${certificationsData.length} records):`);
        certificationsData.forEach((cert, index) => {
            console.log(`${index + 1}. ${cert.certification_name}`);
            console.log(`   Type: ${cert.certification_type} | Issuer: ${cert.issuing_organization}`);
            console.log(`   Issue Date: ${cert.issue_date} | Expiry: ${cert.is_lifetime ? 'Lifetime' : cert.expiry_date}`);
            console.log(`   Status: ${cert.verification_status}`);
            if (cert.skills_covered) {
                const skills = JSON.parse(cert.skills_covered);
                console.log(`   Skills Covered: ${skills.join(', ')}`);
            }
            console.log('');
        });

        // QUERY 5: Skills
        console.log('\n🛠️ QUERY 5: ALL SKILLS BY CATEGORY');
        console.log('=' .repeat(80));
        
        const skillsQuery = `
            SELECT 
                sk.id,
                sk.skill_category,
                sk.skill_name,
                sk.proficiency_level,
                sk.years_of_experience,
                sk.is_certified,
                sk.last_used_date
            FROM staff_skills sk
            WHERE sk.staff_id = ?
            ORDER BY sk.skill_category, sk.proficiency_level DESC
        `;

        console.log(skillsQuery);
        const [skillsData] = await connection.execute(skillsQuery, [teacher.staff_id]);
        
        console.log(`\n✅ SKILLS (${skillsData.length} records):`);
        const skillsByCategory = {};
        skillsData.forEach(skill => {
            if (!skillsByCategory[skill.skill_category]) {
                skillsByCategory[skill.skill_category] = [];
            }
            skillsByCategory[skill.skill_category].push(skill);
        });

        Object.entries(skillsByCategory).forEach(([category, skills]) => {
            console.log(`\n${category.toUpperCase()} (${skills.length} skills):`);
            skills.forEach(skill => {
                console.log(`  • ${skill.skill_name} (${skill.proficiency_level}) - ${skill.years_of_experience} years ${skill.is_certified ? '✓ Certified' : ''}`);
            });
        });

        // SUMMARY
        console.log('\n🎉 COMPLETE DATA RETRIEVAL SUMMARY');
        console.log('=' .repeat(80));
        console.log(`✅ Teacher Information: Retrieved`);
        console.log(`✅ Educational Qualifications: ${educationData.length} records`);
        console.log(`✅ Professional Experience: ${experienceData.length} records`);
        console.log(`   - Previous Experience: ${previousExperience.length} positions`);
        console.log(`   - Current Position: ${currentExperience.length} position`);
        console.log(`✅ Certifications: ${certificationsData.length} records`);
        console.log(`✅ Skills: ${skillsData.length} records across ${Object.keys(skillsByCategory).length} categories`);
        
        console.log('\n🔍 VERIFICATION:');
        console.log(`✅ Gender Column: ${teacher.gender ? 'Working' : 'Missing'}`);
        console.log(`✅ Educational Timeline: ${educationData.length > 0 ? 'Working' : 'Missing'}`);
        console.log(`✅ Previous Experience: ${previousExperience.length > 0 ? 'Working' : 'Missing'}`);

    } catch (error) {
        console.error('Error executing complete teacher queries:', error);
    } finally {
        await connection.end();
    }
}

executeCompleteTeacherQueries();
