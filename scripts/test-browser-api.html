<!DOCTYPE html>
<html>
<head>
    <title>Test Enhanced Teacher API</title>
</head>
<body>
    <h1>Test Enhanced Teacher API</h1>
    <button onclick="testAPI()">Test API Call</button>
    <div id="results"></div>

    <script>
        async function testAPI() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<p>Testing API...</p>';
            
            try {
                console.log('Testing API endpoint...');
                
                const response = await fetch('/principal/api/teacher/profile-enhanced?teacher_id=103', {
                    method: 'GET',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                console.log('API Response:', data);
                
                if (data.success && data.teacher) {
                    resultsDiv.innerHTML = `
                        <h2>✅ API Success!</h2>
                        <p><strong>Teacher:</strong> ${data.teacher.displayName || data.teacher.name}</p>
                        <p><strong>Staff ID:</strong> ${data.teacher.staff_id}</p>
                        <p><strong>Gender:</strong> ${data.teacher.gender}</p>
                        <p><strong>Education Timeline:</strong> ${data.teacher.educationTimeline ? data.teacher.educationTimeline.length : 0} records</p>
                        <p><strong>Experience Timeline:</strong> ${data.teacher.experienceTimeline ? data.teacher.experienceTimeline.length : 0} records</p>
                        <p><strong>Previous Experience:</strong> ${data.teacher.previousExperienceCount || 0} positions</p>
                        
                        <h3>Education Timeline:</h3>
                        <ul>
                            ${data.teacher.educationTimeline ? data.teacher.educationTimeline.map(edu => 
                                `<li>${edu.title} (${edu.year}) - ${edu.percentage}%</li>`
                            ).join('') : '<li>No education data</li>'}
                        </ul>
                        
                        <h3>Experience Timeline:</h3>
                        <ul>
                            ${data.teacher.experienceTimeline ? data.teacher.experienceTimeline.map(exp => 
                                `<li>${exp.title} (${exp.isCurrent ? 'CURRENT' : 'PREVIOUS'}) - ${exp.institution}</li>`
                            ).join('') : '<li>No experience data</li>'}
                        </ul>
                        
                        <h3>Raw Data:</h3>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultsDiv.innerHTML = `
                        <h2>❌ API Error</h2>
                        <p>Error: ${data.message || 'Unknown error'}</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
                
            } catch (error) {
                console.error('Error:', error);
                resultsDiv.innerHTML = `
                    <h2>❌ Request Failed</h2>
                    <p>Error: ${error.message}</p>
                `;
            }
        }
    </script>
</body>
</html>
