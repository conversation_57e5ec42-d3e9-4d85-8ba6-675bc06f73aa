const http = require('http');

async function testActualAPI() {
    try {
        console.log('🌐 TESTING ACTUAL API ENDPOINT\n');

        // Test the API endpoint with proper session simulation
        const options = {
            hostname: 'localhost',
            port: 3018,
            path: '/api/teacher/profile-enhanced?teacher_id=103',
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'Node.js Test Client'
            }
        };

        const req = http.request(options, (res) => {
            console.log(`Status Code: ${res.statusCode}`);
            console.log(`Headers:`, res.headers);

            let data = '';
            res.on('data', (chunk) => {
                data += chunk;
            });

            res.on('end', () => {
                try {
                    if (res.statusCode === 200) {
                        const response = JSON.parse(data);
                        console.log('\n✅ API Response received successfully');
                        
                        if (response.success) {
                            const teacher = response.teacher;
                            console.log('\n📋 TEACHER DATA FROM API:');
                            console.log('=' .repeat(50));
                            console.log(`Name: ${teacher.fullName || teacher.name}`);
                            console.log(`Staff ID: ${teacher.staff_id}`);
                            console.log(`Gender: ${teacher.gender}`);
                            
                            console.log('\n📚 EDUCATION TIMELINE FROM API:');
                            console.log('=' .repeat(50));
                            if (teacher.educationTimeline && teacher.educationTimeline.length > 0) {
                                console.log(`Found ${teacher.educationTimeline.length} education records:`);
                                teacher.educationTimeline.forEach((item, index) => {
                                    console.log(`${index + 1}. ${item.title} (${item.year}) - ${item.percentage}%`);
                                });
                            } else {
                                console.log('❌ No education timeline data in API response');
                                console.log('Education timeline value:', teacher.educationTimeline);
                            }
                            
                            console.log('\n💼 EXPERIENCE TIMELINE FROM API:');
                            console.log('=' .repeat(50));
                            if (teacher.experienceTimeline && teacher.experienceTimeline.length > 0) {
                                console.log(`Found ${teacher.experienceTimeline.length} experience records:`);
                                teacher.experienceTimeline.forEach((item, index) => {
                                    console.log(`${index + 1}. ${item.title} (${item.year}) - ${item.isCurrent ? 'CURRENT' : 'PREVIOUS'}`);
                                });
                            } else {
                                console.log('❌ No experience timeline data in API response');
                                console.log('Experience timeline value:', teacher.experienceTimeline);
                            }
                            
                            console.log('\n📊 SUMMARY STATS FROM API:');
                            console.log('=' .repeat(50));
                            console.log(`Total Qualifications: ${teacher.totalQualifications}`);
                            console.log(`Total Experience: ${teacher.totalExperience}`);
                            console.log(`Total Certifications: ${teacher.totalCertifications}`);
                            console.log(`Total Skills: ${teacher.totalSkills}`);
                            
                        } else {
                            console.log('❌ API returned error:', response.message);
                        }
                    } else {
                        console.log(`❌ API returned status ${res.statusCode}`);
                        console.log('Response body:', data);
                    }
                } catch (parseError) {
                    console.error('Error parsing API response:', parseError);
                    console.log('Raw response:', data);
                }
            });
        });

        req.on('error', (error) => {
            console.error('Request error:', error);
        });

        req.end();

    } catch (error) {
        console.error('Error testing actual API:', error);
    }
}

testActualAPI();
