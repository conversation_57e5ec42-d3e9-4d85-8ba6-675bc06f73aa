const mysql = require('mysql2/promise');

async function checkDisplayedTeachers() {
    const connection = await mysql.createConnection({
        host: 'localhost',
        user: 'root',
        password: '',
        database: 'exam_prep_platform'
    });

    try {
        console.log('🔍 CHECKING TEACHERS DISPLAYED IN TEACHER MANAGEMENT\n');

        // Get teachers that are actually displayed in teacher management (User IDs 11-20)
        const [teachers] = await connection.execute(`
            SELECT u.id, u.name, u.username, s.id as staff_id
            FROM users u
            LEFT JOIN staff s ON u.id = s.user_id
            WHERE u.role = 'teacher' AND u.id BETWEEN 11 AND 20
            ORDER BY u.id
        `);

        console.log(`Found ${teachers.length} teachers in the displayed range:`);
        
        for (const teacher of teachers) {
            console.log(`\n👤 User ID: ${teacher.id}, Name: ${teacher.name}, Staff ID: ${teacher.staff_id}`);
            
            if (teacher.staff_id) {
                // Check enhanced data for this teacher
                const [education] = await connection.execute(`
                    SELECT COUNT(*) as count FROM staff_educational_qualifications WHERE staff_id = ?
                `, [teacher.staff_id]);
                
                const [experience] = await connection.execute(`
                    SELECT COUNT(*) as count FROM staff_professional_experience WHERE staff_id = ?
                `, [teacher.staff_id]);
                
                const [certifications] = await connection.execute(`
                    SELECT COUNT(*) as count FROM staff_certifications WHERE staff_id = ?
                `, [teacher.staff_id]);
                
                const [skills] = await connection.execute(`
                    SELECT COUNT(*) as count FROM staff_skills WHERE staff_id = ?
                `, [teacher.staff_id]);
                
                const totalEnhanced = education[0].count + experience[0].count + certifications[0].count + skills[0].count;
                
                console.log(`   Enhanced Data: ${totalEnhanced} total records`);
                console.log(`   - Education: ${education[0].count}`);
                console.log(`   - Experience: ${experience[0].count}`);
                console.log(`   - Certifications: ${certifications[0].count}`);
                console.log(`   - Skills: ${skills[0].count}`);
                
                if (totalEnhanced > 0) {
                    console.log(`   ✅ HAS ENHANCED DATA - Can test modal with this teacher!`);
                } else {
                    console.log(`   ❌ No enhanced data`);
                }
            } else {
                console.log(`   ❌ No staff_id - cannot have enhanced data`);
            }
        }

        console.log('\n🎯 SUMMARY:');
        const teachersWithData = teachers.filter(async (teacher) => {
            if (!teacher.staff_id) return false;
            const [education] = await connection.execute(`SELECT COUNT(*) as count FROM staff_educational_qualifications WHERE staff_id = ?`, [teacher.staff_id]);
            return education[0].count > 0;
        });
        
        console.log(`Teachers displayed in table: ${teachers.length}`);
        console.log(`Teachers with enhanced data: Need to check individually above`);

    } catch (error) {
        console.error('Error:', error);
    } finally {
        await connection.end();
    }
}

checkDisplayedTeachers();
