const mysql = require('mysql2/promise');

async function debugModalAPICall() {
    const connection = await mysql.createConnection({
        host: 'localhost',
        user: 'root',
        password: '',
        database: 'exam_prep_platform'
    });

    try {
        console.log('🔍 DEBUGGING MODAL API CALL - CHECKING ACTUAL API RESPONSE\n');

        // Test the exact API endpoint that the modal calls
        const teacherId = 103;

        console.log('📋 STEP 1: Testing main teacher query...');
        const [teacherData] = await connection.execute(`
            SELECT
                u.id as user_id, u.username, u.name, u.full_name, u.email, u.role, u.profile_image,
                u.subjects as user_subjects, u.bio, u.date_of_birth, u.created_at, u.last_login, u.is_active,
                u.institution, u.grade, u.field_of_study, u.preferred_subjects, u.target_exams,
                
                s.id as staff_id, s.employee_id, s.designation, s.department, s.current_school,
                s.joining_date, s.employment_type, s.phone, s.alternate_phone, s.emergency_contact,
                s.address, s.city, s.state, s.pincode, s.gender, s.current_salary, s.probation_period_months,
                s.confirmation_date, s.last_promotion_date, s.performance_rating, s.is_on_leave,
                s.office_location, s.subjects_taught, s.classes_handled, s.total_experience_years,
                s.teaching_experience_years, s.administrative_experience_years, s.awards_received,
                s.publications, s.research_papers, s.conferences_attended, s.training_programs,
                s.notes as staff_notes, s.is_active as staff_active,
                
                FLOOR(DATEDIFF(CURDATE(), u.date_of_birth) / 365.25) as calculated_age,
                COALESCE(u.full_name, u.name, u.username) as display_name,
                CASE WHEN u.is_active = 1 THEN 'Active' ELSE 'Inactive' END as account_status
            FROM users u
            LEFT JOIN staff s ON u.id = s.user_id
            WHERE u.id = ? AND u.role = 'teacher'
        `, [teacherId]);

        if (teacherData.length === 0) {
            console.log('❌ No teacher found with ID:', teacherId);
            return;
        }

        const teacher = teacherData[0];
        console.log(`✅ Teacher found: ${teacher.display_name}, Staff ID: ${teacher.staff_id}`);

        if (!teacher.staff_id) {
            console.log('❌ No staff_id found - this will cause empty timelines');
            return;
        }

        console.log('\n📚 STEP 2: Testing education query...');
        const [educationResult] = await connection.execute(`
            SELECT 
                eq.id,
                eq.qualification_level,
                eq.qualification_name,
                eq.specialization,
                eq.institution_name,
                eq.university_board,
                eq.total_marks_obtained,
                eq.total_marks_maximum,
                eq.percentage,
                eq.grade,
                eq.cgpa,
                eq.completion_year,
                eq.subjects,
                eq.achievements,
                eq.thesis_title,
                eq.created_at
            FROM staff_educational_qualifications eq
            WHERE eq.staff_id = ?
            ORDER BY eq.completion_year ASC
        `, [teacher.staff_id]);

        console.log(`Education query returned: ${educationResult.length} records`);

        console.log('\n💼 STEP 3: Testing experience query...');
        const [experienceResult] = await connection.execute(`
            SELECT 
                pe.id,
                pe.job_title,
                pe.organization_name,
                pe.organization_type,
                pe.start_date,
                pe.end_date,
                pe.is_current,
                pe.total_duration_months,
                pe.job_description,
                pe.key_responsibilities,
                pe.achievements,
                pe.skills_used,
                pe.salary_range,
                pe.performance_rating,
                pe.created_at,
                CASE WHEN pe.is_current = 1 THEN 'CURRENT' ELSE 'PREVIOUS' END as position_status
            FROM staff_professional_experience pe
            WHERE pe.staff_id = ?
            ORDER BY pe.start_date ASC
        `, [teacher.staff_id]);

        console.log(`Experience query returned: ${experienceResult.length} records`);

        // Now simulate the exact API mapping
        console.log('\n🔧 STEP 4: Testing API data mapping...');

        const educationTimeline = educationResult.map(item => ({
            title: item.qualification_name,
            institution: item.institution_name,
            year: item.completion_year,
            board: item.university_board,
            specialization: item.specialization,
            percentage: item.percentage,
            grade: item.grade,
            cgpa: item.cgpa,
            totalMarks: item.total_marks_obtained,
            maxMarks: item.total_marks_maximum,
            subjects: item.subjects ? JSON.parse(item.subjects) : null,
            achievements: item.achievements,
            thesis: item.thesis_title,
            level: item.qualification_level,
            type: 'education'
        }));

        const experienceTimeline = experienceResult.map(item => {
            const duration = item.end_date
                ? `${new Date(item.start_date).toLocaleDateString('en-US', {month: 'short', year: 'numeric'})} - ${new Date(item.end_date).toLocaleDateString('en-US', {month: 'short', year: 'numeric'})}`
                : `${new Date(item.start_date).toLocaleDateString('en-US', {month: 'short', year: 'numeric'})} - Present`;

            return {
                title: item.job_title,
                institution: item.organization_name,
                duration: duration,
                description: item.job_description,
                responsibilities: item.key_responsibilities ? JSON.parse(item.key_responsibilities) : [],
                achievements: item.achievements ? JSON.parse(item.achievements) : [],
                skills: item.skills_used ? JSON.parse(item.skills_used) : [],
                isCurrent: item.is_current,
                year: item.start_date ? new Date(item.start_date).getFullYear() : null,
                type: 'experience',
                organizationType: item.organization_type,
                performanceRating: item.performance_rating,
                salaryRange: item.salary_range
            };
        });

        // Sort timelines
        educationTimeline.sort((a, b) => a.year - b.year);
        experienceTimeline.sort((a, b) => a.year - b.year);

        console.log('\n📊 STEP 5: Final API response structure...');
        const apiResponse = {
            success: true,
            teacher: {
                ...teacher,
                educationTimeline,
                experienceTimeline,
                displayName: teacher.display_name,
                primaryEmail: teacher.email,
                age: teacher.calculated_age,
                accountStatus: teacher.account_status,
                totalQualifications: educationTimeline.length,
                totalExperience: experienceTimeline.length,
                previousExperienceCount: experienceTimeline.filter(exp => !exp.isCurrent).length,
                currentPositionCount: experienceTimeline.filter(exp => exp.isCurrent).length
            }
        };

        console.log('\n🎯 API RESPONSE SUMMARY:');
        console.log('=' .repeat(60));
        console.log(`Success: ${apiResponse.success}`);
        console.log(`Teacher Name: ${apiResponse.teacher.displayName}`);
        console.log(`Staff ID: ${apiResponse.teacher.staff_id}`);
        console.log(`Gender: ${apiResponse.teacher.gender}`);
        console.log(`Education Timeline Length: ${apiResponse.teacher.educationTimeline.length}`);
        console.log(`Experience Timeline Length: ${apiResponse.teacher.experienceTimeline.length}`);
        console.log(`Previous Experience Count: ${apiResponse.teacher.previousExperienceCount}`);

        if (apiResponse.teacher.educationTimeline.length === 0) {
            console.log('\n❌ ISSUE: Education timeline is empty!');
        } else {
            console.log('\n✅ Education timeline has data:');
            apiResponse.teacher.educationTimeline.forEach((edu, index) => {
                console.log(`   ${index + 1}. ${edu.title} (${edu.year})`);
            });
        }

        if (apiResponse.teacher.experienceTimeline.length === 0) {
            console.log('\n❌ ISSUE: Experience timeline is empty!');
        } else {
            console.log('\n✅ Experience timeline has data:');
            apiResponse.teacher.experienceTimeline.forEach((exp, index) => {
                console.log(`   ${index + 1}. ${exp.title} (${exp.isCurrent ? 'CURRENT' : 'PREVIOUS'})`);
            });
        }

        console.log('\n🔍 DEBUGGING CONCLUSION:');
        if (apiResponse.teacher.educationTimeline.length > 0 && apiResponse.teacher.experienceTimeline.length > 0) {
            console.log('✅ API data is correct - issue might be in frontend JavaScript');
            console.log('Check browser console for JavaScript errors when opening modal');
        } else {
            console.log('❌ API data is missing - need to check database or queries');
        }

    } catch (error) {
        console.error('Error debugging modal API call:', error);
    } finally {
        await connection.end();
    }
}

debugModalAPICall();
