const mysql = require('mysql2/promise');

async function debugExperienceTimeline() {
    const connection = await mysql.createConnection({
        host: 'localhost',
        user: 'root',
        password: '',
        database: 'exam_prep_platform'
    });

    try {
        console.log('🔍 DEBUGGING EXPERIENCE TIMELINE ISSUE\n');

        // Get CS teacher staff ID
        const [userResult] = await connection.execute(
            'SELECT u.id as user_id, s.id as staff_id FROM users u JOIN staff s ON u.id = s.user_id WHERE u.email = ?',
            ['<EMAIL>']
        );

        if (userResult.length === 0) {
            console.log('❌ CS Teacher not found');
            return;
        }

        const { user_id: userId, staff_id: staffId } = userResult[0];
        console.log(`👨‍🏫 CS Teacher - User ID: ${userId}, Staff ID: ${staffId}\n`);

        // Check if experience data exists in the new table
        console.log('📋 CHECKING PROFESSIONAL EXPERIENCE TABLE:');
        console.log('=' .repeat(60));
        
        const [experienceData] = await connection.execute(`
            SELECT job_title, organization_name, organization_type, start_date, end_date,
                   is_current, total_duration_months, job_description, key_responsibilities,
                   achievements, skills_used, salary_range, performance_rating
            FROM staff_professional_experience
            WHERE staff_id = ?
            ORDER BY start_date ASC
        `, [staffId]);

        console.log(`Found ${experienceData.length} experience records:`);
        experienceData.forEach((item, index) => {
            const duration = item.end_date 
                ? `${new Date(item.start_date).toLocaleDateString('en-US', {month: 'short', year: 'numeric'})} - ${new Date(item.end_date).toLocaleDateString('en-US', {month: 'short', year: 'numeric'})}`
                : `${new Date(item.start_date).toLocaleDateString('en-US', {month: 'short', year: 'numeric'})} - Present`;
            
            console.log(`\n${index + 1}. ${item.job_title} ${item.is_current ? '(CURRENT)' : '(PREVIOUS)'}`);
            console.log(`   Organization: ${item.organization_name}`);
            console.log(`   Duration: ${duration}`);
            console.log(`   Description: ${item.job_description}`);
            console.log(`   Is Current: ${item.is_current}`);
            console.log(`   Start Date: ${item.start_date}`);
            console.log(`   End Date: ${item.end_date || 'NULL (current position)'}`);
        });

        // Test the API mapping logic
        console.log('\n🔧 TESTING API MAPPING LOGIC:');
        console.log('=' .repeat(60));
        
        const mappedExperience = experienceData.map(item => {
            const duration = item.end_date 
                ? `${new Date(item.start_date).toLocaleDateString('en-US', {month: 'short', year: 'numeric'})} - ${new Date(item.end_date).toLocaleDateString('en-US', {month: 'short', year: 'numeric'})}`
                : `${new Date(item.start_date).toLocaleDateString('en-US', {month: 'short', year: 'numeric'})} - Present`;
            
            return {
                title: item.job_title,
                institution: item.organization_name,
                duration: duration,
                description: item.job_description,
                responsibilities: item.key_responsibilities ? JSON.parse(item.key_responsibilities) : [],
                achievements: item.achievements ? JSON.parse(item.achievements) : [],
                skills: item.skills_used ? JSON.parse(item.skills_used) : [],
                isCurrent: item.is_current,
                year: item.start_date ? new Date(item.start_date).getFullYear() : null,
                type: 'experience',
                organizationType: item.organization_type,
                performanceRating: item.performance_rating,
                salaryRange: item.salary_range
            };
        });

        console.log('Mapped experience timeline:');
        mappedExperience.forEach((item, index) => {
            console.log(`\n${index + 1}. ${item.title} (${item.year})`);
            console.log(`   Institution: ${item.institution}`);
            console.log(`   Duration: ${item.duration}`);
            console.log(`   Is Current: ${item.isCurrent}`);
            console.log(`   Type: ${item.type}`);
            console.log(`   Responsibilities: ${item.responsibilities.length} items`);
            console.log(`   Achievements: ${item.achievements.length} items`);
        });

        // Test sorting
        const sortedTimeline = mappedExperience.sort((a, b) => {
            if (a.year && b.year) return a.year - b.year;
            return 0;
        });

        console.log('\n📊 SORTED TIMELINE:');
        console.log('=' .repeat(40));
        sortedTimeline.forEach((item, index) => {
            console.log(`${index + 1}. ${item.title} (${item.year}) - ${item.isCurrent ? 'CURRENT' : 'PREVIOUS'}`);
        });

    } catch (error) {
        console.error('Error debugging experience timeline:', error);
    } finally {
        await connection.end();
    }
}

debugExperienceTimeline();
