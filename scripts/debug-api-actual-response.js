const mysql = require('mysql2/promise');

async function debugAPIActualResponse() {
    const connection = await mysql.createConnection({
        host: 'localhost',
        user: 'root',
        password: '',
        database: 'exam_prep_platform'
    });

    try {
        console.log('🔍 DEBUGGING ACTUAL API RESPONSE STRUCTURE\n');

        const teacherId = 103;

        // Simulate the EXACT API logic from teacher-profile-enhanced-api.js
        console.log('📋 STEP 1: Main teacher query...');
        const [teacherData] = await connection.execute(`
            SELECT
                u.id as user_id, u.username, u.name, u.full_name, u.email, u.role, u.profile_image,
                u.subjects, u.bio, u.date_of_birth, u.created_at, u.last_login, u.is_active,
                u.institution, u.grade, u.field_of_study, u.preferred_subjects, u.target_exams,
                s.id as staff_id, s.employee_id, s.designation, s.department, s.current_school,
                s.joining_date, s.employment_type, s.phone, s.alternate_phone, s.emergency_contact,
                s.address, s.city, s.state, s.pincode, s.gender, s.current_salary, s.probation_period_months,
                s.confirmation_date, s.last_promotion_date, s.performance_rating, s.is_on_leave,
                s.office_location, s.subjects_taught, s.classes_handled, s.total_experience_years,
                s.teaching_experience_years, s.administrative_experience_years, s.awards_received,
                s.publications, s.research_papers, s.conferences_attended, s.training_programs,
                s.notes as staff_notes, s.is_active as staff_active
            FROM users u
            LEFT JOIN staff s ON u.id = s.user_id
            WHERE u.id = ? AND u.role = 'teacher'
        `, [teacherId]);

        const teacher = teacherData[0];
        console.log(`✅ Teacher found: ${teacher.full_name}, Staff ID: ${teacher.staff_id}`);

        // Format user data (exact API logic)
        teacher.displayName = teacher.full_name || teacher.name || teacher.username;
        teacher.primaryEmail = teacher.email;
        teacher.dateOfBirth = teacher.date_of_birth;
        teacher.lastLoginFormatted = teacher.last_login ? new Date(teacher.last_login).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        }) : 'Never logged in';
        teacher.accountCreated = teacher.created_at ? new Date(teacher.created_at).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        }) : 'Unknown';
        teacher.accountStatus = teacher.is_active ? 'Active' : 'Inactive';

        if (teacher.date_of_birth) {
            const today = new Date();
            const birthDate = new Date(teacher.date_of_birth);
            let age = today.getFullYear() - birthDate.getFullYear();
            const monthDiff = today.getMonth() - birthDate.getMonth();
            if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
                age--;
            }
            teacher.age = age;
        }

        // STEP 2: Educational timeline (exact API logic)
        console.log('\n📚 STEP 2: Educational timeline query...');
        let educationTimelineData = [];
        if (teacher.staff_id) {
            const [educationResult] = await connection.execute(`
                SELECT qualification_level, qualification_name, specialization, institution_name,
                       university_board, total_marks_obtained, total_marks_maximum, percentage,
                       grade, cgpa, completion_year, subjects, achievements, thesis_title
                FROM staff_educational_qualifications
                WHERE staff_id = ?
                ORDER BY completion_year ASC
            `, [teacher.staff_id]);
            educationTimelineData = educationResult;
        }

        console.log(`Raw education data: ${educationTimelineData.length} records`);

        let educationTimeline = educationTimelineData.map(item => ({
            title: item.qualification_name,
            institution: item.institution_name,
            year: item.completion_year,
            board: item.university_board,
            specialization: item.specialization,
            percentage: item.percentage,
            grade: item.grade,
            cgpa: item.cgpa,
            totalMarks: item.total_marks_obtained,
            maxMarks: item.total_marks_maximum,
            subjects: item.subjects ? JSON.parse(item.subjects) : null,
            achievements: item.achievements,
            thesis: item.thesis_title,
            level: item.qualification_level,
            type: 'education'
        }));

        console.log(`Mapped education timeline: ${educationTimeline.length} records`);

        // STEP 3: Experience timeline (exact API logic)
        console.log('\n💼 STEP 3: Experience timeline query...');
        let experienceTimelineData = [];
        if (teacher.staff_id) {
            const [experienceResult] = await connection.execute(`
                SELECT job_title, organization_name, organization_type, start_date, end_date,
                       is_current, total_duration_months, job_description, key_responsibilities,
                       achievements, skills_used, salary_range, performance_rating
                FROM staff_professional_experience
                WHERE staff_id = ?
                ORDER BY start_date ASC
            `, [teacher.staff_id]);
            experienceTimelineData = experienceResult;
        }

        console.log(`Raw experience data: ${experienceTimelineData.length} records`);

        let experienceTimeline = experienceTimelineData.map(item => {
            const duration = item.end_date
                ? `${new Date(item.start_date).toLocaleDateString('en-US', {month: 'short', year: 'numeric'})} - ${new Date(item.end_date).toLocaleDateString('en-US', {month: 'short', year: 'numeric'})}`
                : `${new Date(item.start_date).toLocaleDateString('en-US', {month: 'short', year: 'numeric'})} - Present`;

            return {
                title: item.job_title,
                institution: item.organization_name,
                duration: duration,
                description: item.job_description,
                responsibilities: item.key_responsibilities ? JSON.parse(item.key_responsibilities) : [],
                achievements: item.achievements ? JSON.parse(item.achievements) : [],
                skills: item.skills_used ? JSON.parse(item.skills_used) : [],
                isCurrent: item.is_current,
                year: item.start_date ? new Date(item.start_date).getFullYear() : null,
                type: 'experience',
                organizationType: item.organization_type,
                performanceRating: item.performance_rating,
                salaryRange: item.salary_range
            };
        });

        console.log(`Mapped experience timeline: ${experienceTimeline.length} records`);

        // Sort timelines
        educationTimeline.sort((a, b) => a.year - b.year);
        experienceTimeline.sort((a, b) => a.year - b.year);

        // Create final response (exact API logic)
        const enhancedTeacher = {
            ...teacher,
            educationTimeline,
            experienceTimeline,
            totalQualifications: educationTimeline.length,
            totalExperience: experienceTimeline.length,
            fullName: teacher.full_name || teacher.name || teacher.username
        };

        console.log('\n🎯 FINAL API RESPONSE STRUCTURE:');
        console.log('=' .repeat(80));
        console.log(`Teacher Name: ${enhancedTeacher.fullName}`);
        console.log(`Staff ID: ${enhancedTeacher.staff_id}`);
        console.log(`Education Timeline: ${enhancedTeacher.educationTimeline.length} records`);
        console.log(`Experience Timeline: ${enhancedTeacher.experienceTimeline.length} records`);

        console.log('\n📚 EDUCATION TIMELINE IN RESPONSE:');
        if (enhancedTeacher.educationTimeline.length > 0) {
            enhancedTeacher.educationTimeline.forEach((item, index) => {
                console.log(`${index + 1}. ${item.title} (${item.year}) - ${item.percentage}%`);
                console.log(`   Institution: ${item.institution}`);
                console.log(`   Type: ${item.type}`);
                console.log(`   Subjects: ${item.subjects ? Object.keys(item.subjects).length + ' subjects' : 'No subjects'}`);
            });
        } else {
            console.log('❌ NO EDUCATION TIMELINE DATA');
        }

        console.log('\n💼 EXPERIENCE TIMELINE IN RESPONSE:');
        if (enhancedTeacher.experienceTimeline.length > 0) {
            enhancedTeacher.experienceTimeline.forEach((item, index) => {
                console.log(`${index + 1}. ${item.title} (${item.year}) - ${item.isCurrent ? 'CURRENT' : 'PREVIOUS'}`);
                console.log(`   Institution: ${item.institution}`);
                console.log(`   Duration: ${item.duration}`);
                console.log(`   Type: ${item.type}`);
                console.log(`   Responsibilities: ${item.responsibilities.length} items`);
                console.log(`   Achievements: ${item.achievements.length} items`);
            });
        } else {
            console.log('❌ NO EXPERIENCE TIMELINE DATA');
        }

        console.log('\n🔍 DEBUGGING SUMMARY:');
        console.log('=' .repeat(80));
        console.log(`Raw Education Records: ${educationTimelineData.length}`);
        console.log(`Mapped Education Records: ${educationTimeline.length}`);
        console.log(`Raw Experience Records: ${experienceTimelineData.length}`);
        console.log(`Mapped Experience Records: ${experienceTimeline.length}`);
        console.log(`Final Education in Response: ${enhancedTeacher.educationTimeline.length}`);
        console.log(`Final Experience in Response: ${enhancedTeacher.experienceTimeline.length}`);

        // Check if there's a data loss somewhere
        if (educationTimelineData.length > 0 && enhancedTeacher.educationTimeline.length === 0) {
            console.log('⚠️ EDUCATION DATA LOST DURING MAPPING!');
        }
        if (experienceTimelineData.length > 0 && enhancedTeacher.experienceTimeline.length === 0) {
            console.log('⚠️ EXPERIENCE DATA LOST DURING MAPPING!');
        }

        // Show the exact JSON that would be sent to frontend
        console.log('\n📤 EXACT JSON RESPONSE TO FRONTEND:');
        console.log('=' .repeat(80));
        const apiResponse = {
            success: true,
            teacher: enhancedTeacher
        };
        
        console.log('Response structure:');
        console.log(`- success: ${apiResponse.success}`);
        console.log(`- teacher.educationTimeline: ${apiResponse.teacher.educationTimeline ? apiResponse.teacher.educationTimeline.length + ' records' : 'undefined'}`);
        console.log(`- teacher.experienceTimeline: ${apiResponse.teacher.experienceTimeline ? apiResponse.teacher.experienceTimeline.length + ' records' : 'undefined'}`);

        if (apiResponse.teacher.educationTimeline && apiResponse.teacher.educationTimeline.length > 0) {
            console.log('\nEducation timeline sample:');
            console.log(JSON.stringify(apiResponse.teacher.educationTimeline[0], null, 2));
        }

        if (apiResponse.teacher.experienceTimeline && apiResponse.teacher.experienceTimeline.length > 0) {
            console.log('\nExperience timeline sample:');
            console.log(JSON.stringify(apiResponse.teacher.experienceTimeline[0], null, 2));
        }

    } catch (error) {
        console.error('Error debugging API response:', error);
    } finally {
        await connection.end();
    }
}

debugAPIActualResponse();
