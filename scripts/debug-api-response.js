const mysql = require('mysql2/promise');

async function debugAPIResponse() {
    const connection = await mysql.createConnection({
        host: 'localhost',
        user: 'root',
        password: '',
        database: 'exam_prep_platform'
    });

    try {
        console.log('🔍 DEBUGGING API RESPONSE - SIMULATING EXACT API LOGIC\n');

        // Simulate the exact API logic
        const teacherId = 103; // CS teacher user ID

        // Step 1: Get teacher data (exact API query)
        console.log('📋 STEP 1: Getting teacher data...');
        const [teacherData] = await connection.execute(`
            SELECT
                u.id as user_id, u.username, u.name, u.full_name, u.email, u.role, u.profile_image,
                u.subjects, u.bio, u.date_of_birth, u.created_at, u.last_login, u.is_active,
                u.institution, u.grade, u.field_of_study, u.preferred_subjects, u.target_exams,
                s.id as staff_id, s.employee_id, s.designation, s.department, s.current_school,
                s.joining_date, s.employment_type, s.phone, s.alternate_phone, s.emergency_contact,
                s.address, s.city, s.state, s.pincode, s.gender, s.current_salary, s.probation_period_months,
                s.confirmation_date, s.last_promotion_date, s.performance_rating, s.is_on_leave,
                s.office_location, s.subjects_taught, s.classes_handled, s.total_experience_years,
                s.teaching_experience_years, s.administrative_experience_years, s.awards_received,
                s.publications, s.research_papers, s.conferences_attended, s.training_programs,
                s.notes as staff_notes, s.is_active as staff_active
            FROM users u
            LEFT JOIN staff s ON u.id = s.user_id
            WHERE u.id = ? AND u.role = 'teacher'
        `, [teacherId]);

        const teacher = teacherData[0];
        console.log(`✅ Teacher found: ${teacher.full_name}, Staff ID: ${teacher.staff_id}\n`);

        // Step 2: Get educational timeline (exact API logic)
        console.log('📚 STEP 2: Getting educational timeline...');
        let educationTimelineData = [];
        if (teacher.staff_id) {
            const [educationResult] = await connection.execute(`
                SELECT qualification_level, qualification_name, specialization, institution_name,
                       university_board, total_marks_obtained, total_marks_maximum, percentage,
                       grade, cgpa, completion_year, subjects, achievements, thesis_title
                FROM staff_educational_qualifications
                WHERE staff_id = ?
                ORDER BY completion_year ASC
            `, [teacher.staff_id]);
            educationTimelineData = educationResult;
        }

        console.log(`Found ${educationTimelineData.length} education records`);

        // Map education data (exact API logic)
        let educationTimeline = educationTimelineData.map(item => ({
            title: item.qualification_name,
            institution: item.institution_name,
            year: item.completion_year,
            board: item.university_board,
            specialization: item.specialization,
            percentage: item.percentage,
            grade: item.grade,
            cgpa: item.cgpa,
            totalMarks: item.total_marks_obtained,
            maxMarks: item.total_marks_maximum,
            subjects: item.subjects ? JSON.parse(item.subjects) : null,
            achievements: item.achievements,
            thesis: item.thesis_title,
            level: item.qualification_level,
            type: 'education'
        }));

        console.log('Mapped education timeline:');
        educationTimeline.forEach((item, index) => {
            console.log(`${index + 1}. ${item.title} (${item.year}) - ${item.percentage}%`);
        });

        // Step 3: Get experience timeline (exact API logic)
        console.log('\n💼 STEP 3: Getting experience timeline...');
        let experienceTimelineData = [];
        if (teacher.staff_id) {
            const [experienceResult] = await connection.execute(`
                SELECT job_title, organization_name, organization_type, start_date, end_date,
                       is_current, total_duration_months, job_description, key_responsibilities,
                       achievements, skills_used, salary_range, performance_rating
                FROM staff_professional_experience
                WHERE staff_id = ?
                ORDER BY start_date ASC
            `, [teacher.staff_id]);
            experienceTimelineData = experienceResult;
        }

        console.log(`Found ${experienceTimelineData.length} experience records`);

        // Map experience data (exact API logic)
        let experienceTimeline = experienceTimelineData.map(item => {
            const duration = item.end_date
                ? `${new Date(item.start_date).toLocaleDateString('en-US', {month: 'short', year: 'numeric'})} - ${new Date(item.end_date).toLocaleDateString('en-US', {month: 'short', year: 'numeric'})}`
                : `${new Date(item.start_date).toLocaleDateString('en-US', {month: 'short', year: 'numeric'})} - Present`;

            return {
                title: item.job_title,
                institution: item.organization_name,
                duration: duration,
                description: item.job_description,
                responsibilities: item.key_responsibilities ? JSON.parse(item.key_responsibilities) : [],
                achievements: item.achievements ? JSON.parse(item.achievements) : [],
                skills: item.skills_used ? JSON.parse(item.skills_used) : [],
                isCurrent: item.is_current,
                year: item.start_date ? new Date(item.start_date).getFullYear() : null,
                type: 'experience',
                organizationType: item.organization_type,
                performanceRating: item.performance_rating,
                salaryRange: item.salary_range
            };
        });

        console.log('Mapped experience timeline:');
        experienceTimeline.forEach((item, index) => {
            console.log(`${index + 1}. ${item.title} (${item.year}) - ${item.isCurrent ? 'CURRENT' : 'PREVIOUS'}`);
            console.log(`   Duration: ${item.duration}`);
            console.log(`   Responsibilities: ${item.responsibilities.length} items`);
        });

        // Step 4: Sort timelines (exact API logic)
        console.log('\n📊 STEP 4: Sorting timelines...');
        educationTimeline.sort((a, b) => a.year - b.year);
        experienceTimeline.sort((a, b) => a.year - b.year);

        // Step 5: Create final response object (exact API logic)
        console.log('\n🎯 STEP 5: Creating final response object...');
        const enhancedTeacher = {
            ...teacher,
            educationTimeline,
            experienceTimeline,
            totalQualifications: educationTimeline.length,
            totalExperience: experienceTimeline.length,
            fullName: teacher.full_name || teacher.name || teacher.username
        };

        console.log('\n📋 FINAL API RESPONSE STRUCTURE:');
        console.log('=' .repeat(60));
        console.log(`Teacher Name: ${enhancedTeacher.fullName}`);
        console.log(`Staff ID: ${enhancedTeacher.staff_id}`);
        console.log(`Education Timeline Length: ${enhancedTeacher.educationTimeline.length}`);
        console.log(`Experience Timeline Length: ${enhancedTeacher.experienceTimeline.length}`);
        console.log(`Total Qualifications: ${enhancedTeacher.totalQualifications}`);
        console.log(`Total Experience: ${enhancedTeacher.totalExperience}`);

        if (enhancedTeacher.educationTimeline.length > 0) {
            console.log('\n📚 Education Timeline in Response:');
            enhancedTeacher.educationTimeline.forEach((item, index) => {
                console.log(`${index + 1}. ${item.title} (${item.year})`);
            });
        } else {
            console.log('\n❌ Education Timeline is EMPTY in response');
        }

        if (enhancedTeacher.experienceTimeline.length > 0) {
            console.log('\n💼 Experience Timeline in Response:');
            enhancedTeacher.experienceTimeline.forEach((item, index) => {
                console.log(`${index + 1}. ${item.title} (${item.year}) - ${item.isCurrent ? 'CURRENT' : 'PREVIOUS'}`);
            });
        } else {
            console.log('\n❌ Experience Timeline is EMPTY in response');
        }

        // Step 6: Check if there are any issues with the data
        console.log('\n🔧 DIAGNOSTIC INFORMATION:');
        console.log('=' .repeat(60));
        console.log(`Teacher ID: ${teacherId}`);
        console.log(`Staff ID exists: ${teacher.staff_id ? 'YES' : 'NO'}`);
        console.log(`Education query returned: ${educationTimelineData.length} records`);
        console.log(`Experience query returned: ${experienceTimelineData.length} records`);
        console.log(`Education mapping successful: ${educationTimeline.length > 0 ? 'YES' : 'NO'}`);
        console.log(`Experience mapping successful: ${experienceTimeline.length > 0 ? 'YES' : 'NO'}`);

    } catch (error) {
        console.error('Error debugging API response:', error);
    } finally {
        await connection.end();
    }
}

debugAPIResponse();
