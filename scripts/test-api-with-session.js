const mysql = require('mysql2/promise');

async function testApiWithSession() {
    console.log('🔍 TESTING API RESPONSE STRUCTURE\n');

    const connection = await mysql.createConnection({
        host: 'localhost',
        user: 'root',
        password: '',
        database: 'exam_prep_platform'
    });

    try {
        console.log('Testing the exact API response structure...');

        // Test the exact query from the route
        const teacherId = 19;
        console.log(`Testing for teacher ID: ${teacherId}`);

        const [teacherData] = await db.query(`
            SELECT
                -- User Table Data
                u.id as user_id, u.username, u.name, u.full_name, u.email, u.role, u.profile_image,
                u.subjects as user_subjects, u.bio, u.date_of_birth, u.created_at, u.last_login, u.is_active,
                u.institution, u.grade, u.field_of_study, u.preferred_subjects, u.target_exams,

                -- Staff Table Data
                s.id as staff_id, s.employee_id, s.designation, s.department, s.current_school,
                s.joining_date, s.employment_type, s.phone, s.alternate_phone, s.emergency_contact,
                s.address, s.city, s.state, s.pincode, s.gender, s.current_salary, s.probation_period_months,
                s.confirmation_date, s.last_promotion_date, s.performance_rating, s.is_on_leave,
                s.office_location, s.subjects_taught, s.classes_handled, s.total_experience_years,
                s.teaching_experience_years, s.administrative_experience_years, s.awards_received,
                s.publications, s.research_papers, s.conferences_attended, s.training_programs,
                s.notes as staff_notes, s.is_active as staff_active,

                -- Calculated Fields
                FLOOR(DATEDIFF(CURDATE(), u.date_of_birth) / 365.25) as calculated_age,
                COALESCE(u.full_name, u.name, u.username) as display_name,
                CASE WHEN u.is_active = 1 THEN 'Active' ELSE 'Inactive' END as account_status
            FROM users u
            LEFT JOIN staff s ON u.id = s.user_id
            WHERE u.id = ? AND u.role = 'teacher'
        `, [teacherId]);

        if (teacherData.length === 0) {
            console.log('❌ No teacher found');
            return;
        }

        console.log('✅ Teacher found:', teacherData[0].name);
        console.log('Staff ID:', teacherData[0].staff_id);

        // Test education query
        const [educationData] = await db.query(`
            SELECT 
                qualification_level,
                qualification_name,
                specialization,
                institution_name,
                university_board,
                total_marks_obtained,
                total_marks_maximum,
                percentage,
                grade,
                completion_year,
                subjects,
                achievements
            FROM staff_educational_qualifications 
            WHERE staff_id = ?
            ORDER BY completion_year ASC
        `, [teacherData[0].staff_id]);

        console.log('Education records:', educationData.length);

        // Test experience query
        const [experienceData] = await db.query(`
            SELECT 
                job_title,
                organization_name,
                organization_type,
                start_date,
                end_date,
                is_current,
                total_duration_months,
                job_description,
                key_responsibilities,
                achievements,
                skills_used,
                performance_rating,
                salary_range
            FROM staff_professional_experience 
            WHERE staff_id = ?
            ORDER BY start_date DESC
        `, [teacherData[0].staff_id]);

        console.log('Experience records:', experienceData.length);

        // Test certifications query
        const [certificationsData] = await db.query(`
            SELECT 
                certification_name,
                certification_type,
                issuing_organization,
                issue_date,
                expiry_date,
                is_lifetime,
                certificate_id,
                verification_status,
                description,
                skills_covered
            FROM staff_certifications 
            WHERE staff_id = ?
            ORDER BY issue_date DESC
        `, [teacherData[0].staff_id]);

        console.log('Certification records:', certificationsData.length);

        // Test skills query
        const [skillsData] = await db.query(`
            SELECT 
                skill_category,
                skill_name,
                proficiency_level,
                years_of_experience,
                is_certified,
                last_used_date
            FROM staff_skills 
            WHERE staff_id = ?
            ORDER BY skill_category, proficiency_level DESC
        `, [teacherData[0].staff_id]);

        console.log('Skills records:', skillsData.length);

        // Simulate the API response structure
        const mockApiResponse = {
            success: true,
            teacher: {
                ...teacherData[0],
                educationTimeline: educationData.map(edu => ({
                    level: edu.qualification_level,
                    title: edu.qualification_name,
                    institution: edu.institution_name,
                    year: edu.completion_year,
                    grade: edu.grade,
                    percentage: edu.percentage,
                    specialization: edu.specialization,
                    achievements: edu.achievements
                })),
                experienceTimeline: experienceData.map(exp => ({
                    title: exp.job_title,
                    organization: exp.organization_name,
                    startDate: exp.start_date,
                    endDate: exp.end_date,
                    isCurrent: exp.is_current === 1,
                    description: exp.job_description,
                    achievements: exp.achievements
                })),
                certifications: certificationsData.map(cert => ({
                    name: cert.certification_name,
                    type: cert.certification_type,
                    organization: cert.issuing_organization,
                    issueDate: cert.issue_date,
                    skills: cert.skills_covered
                })),
                skillsByCategory: skillsData.reduce((acc, skill) => {
                    if (!acc[skill.skill_category]) {
                        acc[skill.skill_category] = [];
                    }
                    acc[skill.skill_category].push({
                        name: skill.skill_name,
                        level: skill.proficiency_level,
                        experience: skill.years_of_experience
                    });
                    return acc;
                }, {})
            }
        };

        console.log('\n📊 SIMULATED API RESPONSE:');
        console.log('Success:', mockApiResponse.success);
        console.log('Teacher name:', mockApiResponse.teacher.name);
        console.log('Education timeline length:', mockApiResponse.teacher.educationTimeline.length);
        console.log('Experience timeline length:', mockApiResponse.teacher.experienceTimeline.length);
        console.log('Certifications length:', mockApiResponse.teacher.certifications.length);
        console.log('Skills categories:', Object.keys(mockApiResponse.teacher.skillsByCategory));

        console.log('\n✅ API DATA IS AVAILABLE AND PROPERLY STRUCTURED');
        console.log('The issue is likely in the frontend JavaScript or authentication');

    } catch (error) {
        console.error('❌ Error:', error.message);
    }
}

testApiWithSession();
