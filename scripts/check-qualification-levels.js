const mysql = require('mysql2/promise');

async function checkQualificationLevels() {
    const connection = await mysql.createConnection({
        host: 'localhost',
        user: 'root',
        password: '',
        database: 'exam_prep_platform'
    });

    try {
        console.log('🔍 CHECKING QUALIFICATION LEVELS\n');

        // Check existing qualification levels
        const [existing] = await connection.execute(`
            SELECT DISTINCT qualification_level FROM staff_educational_qualifications
        `);

        console.log('Existing qualification levels:');
        existing.forEach(row => {
            console.log(`  - "${row.qualification_level}" (length: ${row.qualification_level.length})`);
        });

        // Check table structure
        const [structure] = await connection.execute(`
            DESCRIBE staff_educational_qualifications
        `);

        console.log('\nTable structure:');
        structure.forEach(row => {
            if (row.Field === 'qualification_level') {
                console.log(`  qualification_level: ${row.Type}`);
            }
        });

    } catch (error) {
        console.error('Error:', error);
    } finally {
        await connection.end();
    }
}

checkQualificationLevels();
