const mysql = require('mysql2/promise');

async function testAPIWithLogs() {
    const connection = await mysql.createConnection({
        host: 'localhost',
        user: 'root',
        password: '',
        database: 'exam_prep_platform'
    });

    try {
        console.log('🔍 TESTING API QUERIES STEP BY STEP\n');

        const teacherId = 103;

        // Step 1: Test main teacher query
        console.log('📋 STEP 1: Main teacher query');
        const [teacherData] = await connection.execute(`
            SELECT
                u.id as user_id, u.username, u.name, u.full_name, u.email, u.role, u.profile_image,
                u.subjects as user_subjects, u.bio, u.date_of_birth, u.created_at, u.last_login, u.is_active,
                u.institution, u.grade, u.field_of_study, u.preferred_subjects, u.target_exams,
                
                s.id as staff_id, s.employee_id, s.designation, s.department, s.current_school,
                s.joining_date, s.employment_type, s.phone, s.alternate_phone, s.emergency_contact,
                s.address, s.city, s.state, s.pincode, s.gender, s.current_salary, s.probation_period_months,
                s.confirmation_date, s.last_promotion_date, s.performance_rating, s.is_on_leave,
                s.office_location, s.subjects_taught, s.classes_handled, s.total_experience_years,
                s.teaching_experience_years, s.administrative_experience_years, s.awards_received,
                s.publications, s.research_papers, s.conferences_attended, s.training_programs,
                s.notes as staff_notes, s.is_active as staff_active,
                
                FLOOR(DATEDIFF(CURDATE(), u.date_of_birth) / 365.25) as calculated_age,
                COALESCE(u.full_name, u.name, u.username) as display_name,
                CASE WHEN u.is_active = 1 THEN 'Active' ELSE 'Inactive' END as account_status
            FROM users u
            LEFT JOIN staff s ON u.id = s.user_id
            WHERE u.id = ? AND u.role = 'teacher'
        `, [teacherId]);

        if (teacherData.length === 0) {
            console.log('❌ No teacher found');
            return;
        }

        const teacher = teacherData[0];
        console.log(`✅ Teacher: ${teacher.display_name}, Staff ID: ${teacher.staff_id}`);

        // Step 2: Test education query
        console.log('\n📚 STEP 2: Education query');
        const [educationResult] = await connection.execute(`
            SELECT
                eq.id,
                eq.qualification_level,
                eq.qualification_name,
                eq.specialization,
                eq.institution_name,
                eq.university_board,
                eq.total_marks_obtained,
                eq.total_marks_maximum,
                eq.percentage,
                eq.grade,
                eq.cgpa,
                eq.completion_year,
                eq.subjects,
                eq.achievements,
                eq.thesis_title,
                eq.created_at
            FROM staff_educational_qualifications eq
            WHERE eq.staff_id = ?
            ORDER BY eq.completion_year ASC
        `, [teacher.staff_id]);

        console.log(`Education query returned: ${educationResult.length} records`);
        
        const educationTimeline = educationResult.map(item => ({
            title: item.qualification_name,
            institution: item.institution_name,
            year: item.completion_year,
            board: item.university_board,
            specialization: item.specialization,
            percentage: item.percentage,
            grade: item.grade,
            cgpa: item.cgpa,
            totalMarks: item.total_marks_obtained,
            maxMarks: item.total_marks_maximum,
            subjects: item.subjects ? JSON.parse(item.subjects) : null,
            achievements: item.achievements,
            thesis: item.thesis_title,
            level: item.qualification_level,
            type: 'education'
        }));

        console.log('Mapped education timeline:', educationTimeline.length, 'records');

        // Step 3: Test experience query
        console.log('\n💼 STEP 3: Experience query');
        const [experienceResult] = await connection.execute(`
            SELECT
                pe.id,
                pe.job_title,
                pe.organization_name,
                pe.organization_type,
                pe.start_date,
                pe.end_date,
                pe.is_current,
                pe.total_duration_months,
                pe.job_description,
                pe.key_responsibilities,
                pe.achievements,
                pe.skills_used,
                pe.salary_range,
                pe.performance_rating,
                pe.created_at,
                CASE WHEN pe.is_current = 1 THEN 'CURRENT' ELSE 'PREVIOUS' END as position_status
            FROM staff_professional_experience pe
            WHERE pe.staff_id = ?
            ORDER BY pe.start_date ASC
        `, [teacher.staff_id]);

        console.log(`Experience query returned: ${experienceResult.length} records`);
        
        const experienceTimeline = experienceResult.map(item => {
            const duration = item.end_date
                ? `${new Date(item.start_date).toLocaleDateString('en-US', {month: 'short', year: 'numeric'})} - ${new Date(item.end_date).toLocaleDateString('en-US', {month: 'short', year: 'numeric'})}`
                : `${new Date(item.start_date).toLocaleDateString('en-US', {month: 'short', year: 'numeric'})} - Present`;

            return {
                title: item.job_title,
                institution: item.organization_name,
                duration: duration,
                description: item.job_description,
                responsibilities: item.key_responsibilities ? JSON.parse(item.key_responsibilities) : [],
                achievements: item.achievements ? JSON.parse(item.achievements) : [],
                skills: item.skills_used ? JSON.parse(item.skills_used) : [],
                isCurrent: item.is_current,
                year: item.start_date ? new Date(item.start_date).getFullYear() : null,
                type: 'experience',
                organizationType: item.organization_type,
                performanceRating: item.performance_rating,
                salaryRange: item.salary_range
            };
        });

        console.log('Mapped experience timeline:', experienceTimeline.length, 'records');

        // Step 4: Test certifications query
        console.log('\n📜 STEP 4: Certifications query');
        const [certificationsResult] = await connection.execute(`
            SELECT
                c.id,
                c.certification_name,
                c.certification_type,
                c.issuing_organization,
                c.issue_date,
                c.expiry_date,
                c.is_lifetime,
                c.certificate_id,
                c.verification_status,
                c.description,
                c.skills_covered
            FROM staff_certifications c
            WHERE c.staff_id = ?
            ORDER BY c.issue_date DESC
        `, [teacher.staff_id]);

        console.log(`Certifications query returned: ${certificationsResult.length} records`);
        
        const certifications = certificationsResult.map(cert => ({
            name: cert.certification_name,
            type: cert.certification_type,
            issuer: cert.issuing_organization,
            issueDate: cert.issue_date,
            expiryDate: cert.expiry_date,
            isLifetime: cert.is_lifetime,
            certificateId: cert.certificate_id,
            status: cert.verification_status,
            description: cert.description,
            skillsCovered: cert.skills_covered ? JSON.parse(cert.skills_covered) : []
        }));

        console.log('Mapped certifications:', certifications.length, 'records');

        // Step 5: Test skills query
        console.log('\n🛠️ STEP 5: Skills query');
        const [skillsResult] = await connection.execute(`
            SELECT
                sk.id,
                sk.skill_category,
                sk.skill_name,
                sk.proficiency_level,
                sk.years_of_experience,
                sk.is_certified,
                sk.last_used_date
            FROM staff_skills sk
            WHERE sk.staff_id = ?
            ORDER BY sk.skill_category, sk.proficiency_level DESC
        `, [teacher.staff_id]);

        console.log(`Skills query returned: ${skillsResult.length} records`);
        
        const skillsByCategory = {};
        skillsResult.forEach(skill => {
            if (!skillsByCategory[skill.skill_category]) {
                skillsByCategory[skill.skill_category] = [];
            }
            skillsByCategory[skill.skill_category].push({
                name: skill.skill_name,
                proficiency: skill.proficiency_level,
                experience: skill.years_of_experience,
                certified: skill.is_certified,
                lastUsed: skill.last_used_date
            });
        });

        console.log('Mapped skills by category:', Object.keys(skillsByCategory).length, 'categories');

        // Step 6: Create final response
        console.log('\n🎯 STEP 6: Final API response structure');
        const enhancedTeacher = {
            ...teacher,
            educationTimeline,
            experienceTimeline,
            certifications,
            skillsByCategory,
            
            staff_id: teacher.staff_id,
            user_id: teacher.user_id,
            displayName: teacher.display_name,
            primaryEmail: teacher.email,
            age: teacher.calculated_age,
            accountStatus: teacher.account_status,
            
            totalQualifications: educationTimeline.length,
            totalExperience: experienceTimeline.length,
            previousExperienceCount: experienceTimeline.filter(exp => !exp.isCurrent).length,
            currentPositionCount: experienceTimeline.filter(exp => exp.isCurrent).length,
            totalCertifications: certifications.length,
            totalSkills: skillsResult.length,
            skillCategoriesCount: Object.keys(skillsByCategory).length
        };

        console.log('\n📊 FINAL RESPONSE SUMMARY:');
        console.log('=' .repeat(60));
        console.log(`Teacher Name: ${enhancedTeacher.displayName}`);
        console.log(`Staff ID: ${enhancedTeacher.staff_id}`);
        console.log(`Education Timeline: ${enhancedTeacher.totalQualifications} records`);
        console.log(`Experience Timeline: ${enhancedTeacher.totalExperience} records`);
        console.log(`Previous Experience: ${enhancedTeacher.previousExperienceCount} positions`);
        console.log(`Current Position: ${enhancedTeacher.currentPositionCount} position`);
        console.log(`Certifications: ${enhancedTeacher.totalCertifications} records`);
        console.log(`Skills: ${enhancedTeacher.totalSkills} records`);
        console.log(`Skill Categories: ${enhancedTeacher.skillCategoriesCount} categories`);

        if (enhancedTeacher.totalQualifications === 0) {
            console.log('\n❌ ISSUE: Education timeline is empty!');
        }
        if (enhancedTeacher.totalExperience === 0) {
            console.log('\n❌ ISSUE: Experience timeline is empty!');
        }
        if (enhancedTeacher.totalCertifications === 0) {
            console.log('\n❌ ISSUE: Certifications are empty!');
        }
        if (enhancedTeacher.totalSkills === 0) {
            console.log('\n❌ ISSUE: Skills are empty!');
        }

        console.log('\n✅ API QUERIES TEST COMPLETED');

    } catch (error) {
        console.error('Error testing API queries:', error);
    } finally {
        await connection.end();
    }
}

testAPIWithLogs();
