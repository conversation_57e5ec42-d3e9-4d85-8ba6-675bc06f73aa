const mysql = require('mysql2/promise');

async function debugEducationTimeline() {
    const connection = await mysql.createConnection({
        host: 'localhost',
        user: 'root',
        password: '',
        database: 'exam_prep_platform'
    });

    try {
        console.log('🔍 DEBUGGING EDUCATION TIMELINE ISSUE\n');

        // Get CS teacher staff ID
        const [userResult] = await connection.execute(
            'SELECT u.id as user_id, s.id as staff_id FROM users u JOIN staff s ON u.id = s.user_id WHERE u.email = ?',
            ['<EMAIL>']
        );

        if (userResult.length === 0) {
            console.log('❌ CS Teacher not found');
            return;
        }

        const { user_id: userId, staff_id: staffId } = userResult[0];
        console.log(`👨‍🏫 CS Teacher - User ID: ${userId}, Staff ID: ${staffId}\n`);

        // Check if education data exists in the new table
        console.log('📋 CHECKING EDUCATIONAL QUALIFICATIONS TABLE:');
        console.log('=' .repeat(60));
        
        const [educationData] = await connection.execute(`
            SELECT qualification_level, qualification_name, specialization, institution_name,
                   university_board, total_marks_obtained, total_marks_maximum, percentage,
                   grade, cgpa, completion_year, subjects, achievements, thesis_title
            FROM staff_educational_qualifications
            WHERE staff_id = ?
            ORDER BY completion_year ASC
        `, [staffId]);

        console.log(`Found ${educationData.length} education records:`);
        educationData.forEach((item, index) => {
            console.log(`\n${index + 1}. ${item.qualification_name} (${item.completion_year})`);
            console.log(`   Institution: ${item.institution_name}`);
            console.log(`   Board/University: ${item.university_board}`);
            console.log(`   Percentage: ${item.percentage}%`);
            console.log(`   Grade: ${item.grade || 'Not specified'}`);
            console.log(`   CGPA: ${item.cgpa || 'Not specified'}`);
            console.log(`   Specialization: ${item.specialization || 'Not specified'}`);
            if (item.subjects) {
                const subjects = JSON.parse(item.subjects);
                console.log(`   Subjects: ${Object.keys(subjects).length} subjects`);
            }
            if (item.achievements) {
                console.log(`   Achievements: ${item.achievements}`);
            }
            if (item.thesis_title) {
                console.log(`   Thesis: ${item.thesis_title}`);
            }
        });

        // If no data found, check if we need to populate it
        if (educationData.length === 0) {
            console.log('\n⚠️ No education data found in staff_educational_qualifications table');
            console.log('🔧 Let me check if we have sample data to populate...\n');
            
            // Check if the table exists and has any data at all
            const [tableCheck] = await connection.execute(`
                SELECT COUNT(*) as total_records FROM staff_educational_qualifications
            `);
            
            console.log(`Total records in staff_educational_qualifications table: ${tableCheck[0].total_records}`);
            
            if (tableCheck[0].total_records === 0) {
                console.log('\n📝 Table is empty. Let me populate it with CS teacher data...');
                
                // Insert CS teacher educational qualifications
                const educationRecords = [
                    {
                        staff_id: staffId,
                        qualification_level: 'secondary',
                        qualification_name: 'Secondary School Certificate',
                        specialization: 'Science',
                        institution_name: 'Government High School, Ludhiana',
                        university_board: 'Punjab School Education Board (PSEB)',
                        total_marks_obtained: 442,
                        total_marks_maximum: 500,
                        percentage: 88.40,
                        grade: 'A+',
                        completion_year: 2007,
                        subjects: JSON.stringify({
                            "Mathematics": {"marks": 95, "total": 100},
                            "Science": {"marks": 92, "total": 100},
                            "English": {"marks": 85, "total": 100},
                            "Hindi": {"marks": 88, "total": 100},
                            "Social Science": {"marks": 82, "total": 100}
                        }),
                        achievements: 'School topper in Mathematics and Science'
                    },
                    {
                        staff_id: staffId,
                        qualification_level: 'higher_secondary',
                        qualification_name: 'Higher Secondary Certificate',
                        specialization: 'Science (PCM)',
                        institution_name: 'DAV College, Ludhiana',
                        university_board: 'Punjab School Education Board (PSEB)',
                        total_marks_obtained: 459,
                        total_marks_maximum: 500,
                        percentage: 91.80,
                        grade: 'A+',
                        completion_year: 2009,
                        subjects: JSON.stringify({
                            "Physics": {"marks": 96, "total": 100},
                            "Chemistry": {"marks": 94, "total": 100},
                            "Mathematics": {"marks": 98, "total": 100},
                            "English": {"marks": 87, "total": 100},
                            "Computer Science": {"marks": 84, "total": 100}
                        }),
                        achievements: 'District rank 15 in PCM stream'
                    },
                    {
                        staff_id: staffId,
                        qualification_level: 'bachelor',
                        qualification_name: 'Bachelor of Technology',
                        specialization: 'Computer Science and Engineering',
                        institution_name: 'Punjab Technical University',
                        university_board: 'Punjab Technical University',
                        total_marks_obtained: 3558,
                        total_marks_maximum: 4000,
                        percentage: 88.97,
                        grade: 'A',
                        cgpa: 8.9,
                        completion_year: 2011,
                        subjects: JSON.stringify({
                            "Data Structures": {"marks": 92, "total": 100},
                            "Algorithms": {"marks": 95, "total": 100},
                            "Database Management": {"marks": 88, "total": 100},
                            "Computer Networks": {"marks": 85, "total": 100},
                            "Software Engineering": {"marks": 90, "total": 100},
                            "Operating Systems": {"marks": 87, "total": 100},
                            "Web Technologies": {"marks": 89, "total": 100},
                            "Project Work": {"marks": 94, "total": 100}
                        }),
                        achievements: 'Best Project Award for Educational Management System'
                    }
                ];

                for (const record of educationRecords) {
                    await connection.execute(`
                        INSERT INTO staff_educational_qualifications 
                        (staff_id, qualification_level, qualification_name, specialization, institution_name,
                         university_board, total_marks_obtained, total_marks_maximum, percentage, grade, cgpa,
                         completion_year, subjects, achievements, thesis_title)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    `, [
                        record.staff_id, record.qualification_level, record.qualification_name,
                        record.specialization, record.institution_name, record.university_board,
                        record.total_marks_obtained, record.total_marks_maximum, record.percentage,
                        record.grade, record.cgpa, record.completion_year, record.subjects,
                        record.achievements, record.thesis_title || null
                    ]);
                }

                console.log('✅ Educational qualifications populated successfully!');
                
                // Re-fetch the data to verify
                const [newEducationData] = await connection.execute(`
                    SELECT qualification_level, qualification_name, specialization, institution_name,
                           university_board, total_marks_obtained, total_marks_maximum, percentage,
                           grade, cgpa, completion_year, subjects, achievements, thesis_title
                    FROM staff_educational_qualifications
                    WHERE staff_id = ?
                    ORDER BY completion_year ASC
                `, [staffId]);

                console.log(`\n✅ Verification - Found ${newEducationData.length} education records after population:`);
                newEducationData.forEach((item, index) => {
                    console.log(`${index + 1}. ${item.qualification_name} (${item.completion_year}) - ${item.percentage}%`);
                });
            }
        }

        console.log('\n✅ Education timeline debugging completed!');

    } catch (error) {
        console.error('Error debugging education timeline:', error);
    } finally {
        await connection.end();
    }
}

debugEducationTimeline();
