<!DOCTYPE html>
<html>
<head>
    <title>Test Enhanced Teacher API</title>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; }
        pre { white-space: pre-wrap; word-wrap: break-word; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h1>Enhanced Teacher API Test</h1>
    <p>This page tests the enhanced teacher profile API endpoint.</p>
    
    <button onclick="testAPI()">Test API for Teacher ID 103</button>
    <button onclick="testAPIWithoutAuth()">Test Without Auth</button>
    <button onclick="clearResults()">Clear Results</button>
    
    <div id="results"></div>

    <script>
        async function testAPI() {
            const resultsDiv = document.getElementById('results');
            addResult('🧪 Testing Enhanced Teacher API...', 'info');
            
            try {
                console.log('Making API request...');
                
                const response = await fetch('/principal/api/teacher/profile-enhanced?teacher_id=103', {
                    method: 'GET',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                console.log('Response received:', response);
                addResult(`📡 Response Status: ${response.status} ${response.statusText}`, 'info');
                
                if (!response.ok) {
                    if (response.status === 401) {
                        addResult('❌ Authentication Error: Please log in as Principal first', 'error');
                        addResult('💡 Go to <a href="/login">Login Page</a> and log in as Principal', 'info');
                        return;
                    }
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                console.log('API Response:', data);
                
                if (data.success && data.teacher) {
                    addResult('✅ API Success!', 'success');
                    addResult(`👤 Teacher: ${data.teacher.displayName || data.teacher.name}`, 'success');
                    addResult(`🆔 Staff ID: ${data.teacher.staff_id}`, 'success');
                    addResult(`👫 Gender: ${data.teacher.gender}`, 'success');
                    addResult(`📚 Education Timeline: ${data.teacher.educationTimeline ? data.teacher.educationTimeline.length : 0} records`, 'success');
                    addResult(`💼 Experience Timeline: ${data.teacher.experienceTimeline ? data.teacher.experienceTimeline.length : 0} records`, 'success');
                    addResult(`📈 Previous Experience: ${data.teacher.previousExperienceCount || 0} positions`, 'success');
                    
                    if (data.teacher.educationTimeline && data.teacher.educationTimeline.length > 0) {
                        addResult('📚 Education Records:', 'success');
                        data.teacher.educationTimeline.forEach((edu, index) => {
                            addResult(`   ${index + 1}. ${edu.title} (${edu.year}) - ${edu.percentage}%`, 'success');
                        });
                    }
                    
                    if (data.teacher.experienceTimeline && data.teacher.experienceTimeline.length > 0) {
                        addResult('💼 Experience Records:', 'success');
                        data.teacher.experienceTimeline.forEach((exp, index) => {
                            addResult(`   ${index + 1}. ${exp.title} (${exp.isCurrent ? 'CURRENT' : 'PREVIOUS'}) - ${exp.institution}`, 'success');
                        });
                    }
                    
                    addResult('📄 Full API Response:', 'info');
                    addResult(`<pre>${JSON.stringify(data, null, 2)}</pre>`, 'info');
                    
                } else {
                    addResult('❌ API Error', 'error');
                    addResult(`Error: ${data.message || 'Unknown error'}`, 'error');
                    addResult(`<pre>${JSON.stringify(data, null, 2)}</pre>`, 'error');
                }
                
            } catch (error) {
                console.error('Error:', error);
                addResult('❌ Request Failed', 'error');
                addResult(`Error: ${error.message}`, 'error');
            }
        }
        
        async function testAPIWithoutAuth() {
            addResult('🧪 Testing API without authentication...', 'info');
            
            try {
                const response = await fetch('/principal/api/teacher/profile-enhanced?teacher_id=103', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                    // No credentials: 'include'
                });
                
                addResult(`📡 Response Status: ${response.status} ${response.statusText}`, 'info');
                
                if (response.status === 401) {
                    addResult('✅ Correct: API requires authentication (401 Unauthorized)', 'success');
                } else {
                    addResult('⚠️ Unexpected: API should require authentication', 'error');
                }
                
            } catch (error) {
                addResult(`❌ Error: ${error.message}`, 'error');
            }
        }
        
        function addResult(message, type) {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            resultsDiv.appendChild(resultDiv);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        // Test on page load
        document.addEventListener('DOMContentLoaded', function() {
            addResult('🚀 Enhanced Teacher API Test Page Loaded', 'info');
            addResult('💡 Click "Test API" to test the enhanced teacher profile endpoint', 'info');
            addResult('⚠️ Make sure you are logged in as Principal first', 'info');
        });
    </script>
</body>
</html>
