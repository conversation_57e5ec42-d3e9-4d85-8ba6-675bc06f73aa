// Test script to run in browser console
// Copy and paste this into the browser console on the teacher management page

console.log('🧪 Testing Enhanced Teacher Modal API');

// Test 1: Check if jQuery is loaded
if (typeof $ === 'undefined') {
    console.error('❌ jQuery is not loaded');
} else {
    console.log('✅ jQuery is loaded');
}

// Test 2: Check if modal functions exist
if (typeof window.openEnhancedTeacherModal === 'function') {
    console.log('✅ openEnhancedTeacherModal function exists');
} else {
    console.error('❌ openEnhancedTeacherModal function not found');
}

// Test 3: Test API call directly
async function testAPICall() {
    console.log('🔍 Testing API call...');
    
    try {
        const response = await fetch('/principal/api/teacher/profile-enhanced?teacher_id=103', {
            method: 'GET',
            credentials: 'include',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        console.log('📡 Response status:', response.status);
        
        if (!response.ok) {
            console.error('❌ API call failed:', response.status, response.statusText);
            return;
        }
        
        const data = await response.json();
        console.log('✅ API call successful');
        console.log('📊 Response data:', data);
        
        if (data.success && data.teacher) {
            console.log('✅ Teacher data received:');
            console.log('  - Name:', data.teacher.displayName || data.teacher.name);
            console.log('  - Staff ID:', data.teacher.staff_id);
            console.log('  - Gender:', data.teacher.gender);
            console.log('  - Education Timeline:', data.teacher.educationTimeline ? data.teacher.educationTimeline.length : 0, 'records');
            console.log('  - Experience Timeline:', data.teacher.experienceTimeline ? data.teacher.experienceTimeline.length : 0, 'records');
            console.log('  - Previous Experience:', data.teacher.previousExperienceCount || 0, 'positions');
            
            // Test modal display
            if (typeof window.displayEnhancedTeacherProfile === 'function') {
                console.log('🎯 Testing modal display...');
                window.displayEnhancedTeacherProfile(data.teacher);
                console.log('✅ Modal display function called');
            } else {
                console.error('❌ displayEnhancedTeacherProfile function not found');
            }
        } else {
            console.error('❌ Invalid API response:', data);
        }
        
    } catch (error) {
        console.error('❌ API test failed:', error);
    }
}

// Test 4: Test modal opening
function testModalOpen() {
    console.log('🎯 Testing modal open...');
    
    if (typeof window.openEnhancedTeacherModal === 'function') {
        window.openEnhancedTeacherModal(103);
        console.log('✅ Modal open function called');
    } else {
        console.error('❌ Modal open function not available');
    }
}

// Test 5: Check if modal HTML exists
function checkModalHTML() {
    console.log('🔍 Checking modal HTML...');
    
    const modal = document.getElementById('teacherModal');
    if (modal) {
        console.log('✅ Modal HTML exists');
        
        const educationTimeline = document.getElementById('modal-education-timeline');
        const experienceTimeline = document.getElementById('modal-experience-timeline');
        
        console.log('  - Education timeline element:', educationTimeline ? 'EXISTS' : 'MISSING');
        console.log('  - Experience timeline element:', experienceTimeline ? 'EXISTS' : 'MISSING');
    } else {
        console.error('❌ Modal HTML not found');
    }
}

// Run all tests
console.log('🚀 Running all tests...');
checkModalHTML();
testAPICall();

console.log('📝 Manual tests available:');
console.log('  - testModalOpen() - Test opening the modal');
console.log('  - testAPICall() - Test the API call');
console.log('  - checkModalHTML() - Check modal HTML elements');
