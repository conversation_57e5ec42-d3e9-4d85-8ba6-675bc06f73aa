// Simple jsPDF implementation for basic PDF generation
// This is a minimal version to avoid CDN loading issues

(function() {
    'use strict';
    
    // Simple PDF generator class
    function SimplePDF() {
        this.content = [];
        this.pageHeight = 297; // A4 height in mm
        this.pageWidth = 210;  // A4 width in mm
        this.currentY = 20;
        this.lineHeight = 6;
    }
    
    SimplePDF.prototype.text = function(text, x, y) {
        this.content.push({
            type: 'text',
            text: text,
            x: x,
            y: y
        });
        return this;
    };
    
    SimplePDF.prototype.save = function(filename) {
        // Create a simple HTML page that can be printed as PDF
        const htmlContent = this.generateHTML();
        const printWindow = window.open('', '_blank');
        printWindow.document.write(htmlContent);
        printWindow.document.close();
        
        // Wait for content to load then trigger print
        setTimeout(() => {
            printWindow.print();
            setTimeout(() => {
                printWindow.close();
            }, 1000);
        }, 500);
        
        return this;
    };
    
    SimplePDF.prototype.generateHTML = function() {
        let html = `
        <!DOCTYPE html>
        <html>
        <head>
            <title>PDF Document</title>
            <style>
                @page {
                    size: A4;
                    margin: 20mm;
                }
                body {
                    font-family: Arial, sans-serif;
                    margin: 0;
                    padding: 0;
                    line-height: 1.4;
                }
                .pdf-content {
                    position: relative;
                    width: 170mm;
                    min-height: 257mm;
                }
                .text-item {
                    position: absolute;
                    font-size: 12px;
                }
                @media print {
                    body { margin: 0; }
                    .no-print { display: none; }
                }
            </style>
        </head>
        <body>
            <div class="no-print" style="padding: 20px; background: #f0f0f0; margin-bottom: 20px;">
                <h3>PDF Preview</h3>
                <p>This document will be printed as PDF. Use your browser's print function (Ctrl+P) and select "Save as PDF".</p>
                <button onclick="window.print()">Print/Save as PDF</button>
                <button onclick="window.close()">Close</button>
            </div>
            <div class="pdf-content">
        `;
        
        this.content.forEach(item => {
            if (item.type === 'text') {
                // Convert mm to pixels (approximate)
                const xPx = item.x * 3.78; // 1mm ≈ 3.78px
                const yPx = item.y * 3.78;
                
                html += `<div class="text-item" style="left: ${xPx}px; top: ${yPx}px;">${this.escapeHtml(item.text)}</div>`;
            }
        });
        
        html += `
            </div>
        </body>
        </html>
        `;
        
        return html;
    };
    
    SimplePDF.prototype.escapeHtml = function(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    };
    
    // Make it available globally
    window.SimplePDF = SimplePDF;

    // Create jsPDF constructor function
    function jsPDF() {
        return new SimplePDF();
    }

    // Make jsPDF available globally
    window.jsPDF = jsPDF;

    // For compatibility with different loading patterns
    window.jspdf = {
        jsPDF: jsPDF
    };
    
    console.log('✅ Simple PDF library loaded successfully');
})();
