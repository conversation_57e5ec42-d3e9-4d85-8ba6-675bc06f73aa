// Enhanced Teacher Modal JavaScript
// This file contains all the enhanced teacher modal functionality

// Set global flag to prevent conflicts
window.enhancedTeacherModalLoaded = true;
console.log('Enhanced teacher modal JS loaded');

// Enhanced Teacher Modal Functionality
$(document).ready(function() {
    console.log('Enhanced teacher modal DOM ready');
    
    // Remove any existing event handlers from external JS
    $('.view-teacher-btn').off('click');
    $('[id^="viewTeacherBtn-"]').off('click');
    
    // View teacher details button (class-based)
    $(document).on('click', '.view-teacher-btn', function(e) {
        e.preventDefault();
        e.stopPropagation();
        const teacherId = $(this).data('teacher-id');
        openEnhancedTeacherModal(teacherId);
    });

    // View teacher details button (ID-based)
    $(document).on('click', '[id^="viewTeacherBtn-"]', function(e) {
        e.preventDefault();
        e.stopPropagation();
        const teacherId = $(this).data('teacher-id');
        if (!teacherId) {
            // Extract from button ID if data attribute is missing
            const buttonId = $(this).attr('id');
            const extractedId = buttonId.replace('viewTeacherBtn-', '');
            if (extractedId) {
                openEnhancedTeacherModal(extractedId);
                return;
            }
        }
        openEnhancedTeacherModal(teacherId);
    });

    // Close modal buttons
    $(document).on('click', '#closeTeacherModalBtn, #closeTeacherModalBtn2', function() {
        closeEnhancedTeacherModal();
    });

    // Print teacher profile
    $(document).on('click', '#printTeacherProfile', function() {
        window.print();
    });

    // Close modal when clicking outside
    $(document).on('click', '#teacherModal', function(e) {
        if (e.target === this) {
            closeEnhancedTeacherModal();
        }
    });
});

// Open enhanced teacher modal (make it globally available)
window.openEnhancedTeacherModal = function(teacherId) {
    console.log('Enhanced modal function called for teacher ID:', teacherId);
    console.log('Enhanced modal elements check starting...');
    console.log('Function defined as window.openEnhancedTeacherModal:', typeof window.openEnhancedTeacherModal);

    const modal = document.getElementById('teacherModal');
    const loadingDiv = document.getElementById('modal-loading');
    const contentDiv = document.getElementById('enhanced-profile-content');

    if (!modal || !loadingDiv || !contentDiv) {
        console.error('Enhanced modal elements not found');
        alert('Error: Enhanced modal elements not found');
        return;
    }

    // Show modal and loading state
    modal.classList.remove('hidden');
    loadingDiv.classList.remove('hidden');
    contentDiv.classList.add('hidden');

    console.log('Fetching enhanced teacher data from API...');
    console.log('API URL:', `/principal/api/teacher/profile-enhanced?teacher_id=${teacherId}`);

    // Fetch enhanced teacher data
    fetch(`/principal/api/teacher/profile-enhanced?teacher_id=${teacherId}`, {
        method: 'GET',
        credentials: 'include', // Include cookies for session authentication
        headers: {
            'Content-Type': 'application/json'
        }
    })
        .then(response => {
            console.log('API response status:', response.status);
            console.log('API response headers:', response.headers);
            if (!response.ok) {
                console.error(`HTTP error! status: ${response.status}`);
                if (response.status === 302) {
                    console.error('Redirected to login - authentication issue');
                }
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(result => {
            console.log('🔍 API Response:', result);
            console.log('🔍 Teacher Data:', result.teacher);
            console.log('🔍 Education Timeline:', result.teacher?.educationTimeline);
            console.log('🔍 Experience Timeline:', result.teacher?.experienceTimeline);

            if (result.success) {
                displayEnhancedTeacherProfile(result.teacher);
                loadingDiv.classList.add('hidden');
                contentDiv.classList.remove('hidden');
            } else {
                console.error('API returned error:', result.message);
                showModalError(result.message || 'Error loading teacher profile');
            }
        })
        .catch(error => {
            console.error('Error fetching teacher profile:', error);
            console.error('Error details:', error.message);
            showModalError(`Failed to load teacher profile: ${error.message}. Please try again.`);
        });
};

// Close enhanced teacher modal (make it globally available)
window.closeEnhancedTeacherModal = function() {
    const modal = document.getElementById('teacherModal');
    modal.classList.add('hidden');
};

// Override external JS function
window.openTeacherModal = function(teacherId) {
    console.log('External openTeacherModal called, redirecting to enhanced modal for teacher:', teacherId);
    console.log('Enhanced modal function available:', typeof window.openEnhancedTeacherModal);
    if (window.openEnhancedTeacherModal) {
        window.openEnhancedTeacherModal(teacherId);
    } else {
        console.error('Enhanced modal not yet available, waiting...');
        setTimeout(function() {
            if (window.openEnhancedTeacherModal) {
                console.log('Enhanced modal now available, calling it');
                window.openEnhancedTeacherModal(teacherId);
            } else {
                console.error('Enhanced modal still not available after delay');
            }
        }, 100);
    }
};

window.closeTeacherModal = function() {
    console.log('External closeTeacherModal called, redirecting to enhanced modal close');
    if (window.closeEnhancedTeacherModal) {
        window.closeEnhancedTeacherModal();
    }
};

// Send message functionality
window.sendMessage = function(teacherId) {
    console.log('Send message functionality for teacher ID:', teacherId);
    alert(`Message functionality for teacher ID: ${teacherId} - To be implemented`);
};

// Log that the enhanced modal functions are now available
console.log('Enhanced modal functions defined and available:');
console.log('- openEnhancedTeacherModal:', typeof window.openEnhancedTeacherModal);
console.log('- closeEnhancedTeacherModal:', typeof window.closeEnhancedTeacherModal);
console.log('- displayEnhancedTeacherProfile:', typeof displayEnhancedTeacherProfile);

// Test if all required functions are available
setTimeout(() => {
    console.log('🔍 Function availability check:');
    console.log('- displayEnhancedTeacherProfile:', typeof displayEnhancedTeacherProfile);
    console.log('- displayModalEducationTimeline:', typeof displayModalEducationTimeline);
    console.log('- displayModalExperienceTimeline:', typeof displayModalExperienceTimeline);
    console.log('- displayModalEnhancedCertifications:', typeof displayModalEnhancedCertifications);
    console.log('- displayModalEnhancedSkills:', typeof displayModalEnhancedSkills);
}, 1000);
