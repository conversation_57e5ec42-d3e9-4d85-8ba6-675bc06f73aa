// Teacher Profile PDF Generator - Professional Resume/CV Format
// Uses jsPDF library for PDF generation

class TeacherProfilePDFGenerator {
    constructor() {
        this.doc = null;
        this.currentY = 20;
        this.pageHeight = 297; // A4 height in mm
        this.pageWidth = 210; // A4 width in mm
        this.margin = 20;
        this.lineHeight = 6;
        this.sectionSpacing = 8;
        this.colors = {
            primary: '#2563eb',
            secondary: '#64748b',
            accent: '#059669',
            text: '#1f2937',
            light: '#f8fafc'
        };
    }

    async generatePDF(teacherData) {
        console.log('🔄 Starting PDF generation for teacher:', teacherData.name);
        
        try {
            // Initialize jsPDF (handle both global and module versions)
            const { jsPDF } = window.jspdf || window;
            this.doc = new jsPDF('p', 'mm', 'a4');
            this.currentY = this.margin;

            // Generate CV sections in reverse chronological order
            this.addHeader(teacherData);
            this.addPersonalSummary(teacherData);
            this.addContactInformation(teacherData);
            this.addProfessionalExperience(teacherData);
            this.addEducation(teacherData);
            this.addAchievements(teacherData);
            this.addSkills(teacherData);
            this.addCertifications(teacherData);
            this.addPublications(teacherData);
            this.addAdditionalInformation(teacherData);

            // Generate filename
            const fileName = `${teacherData.name.replace(/\s+/g, '_')}_CV_${new Date().toISOString().split('T')[0]}.pdf`;
            
            // Save the PDF
            this.doc.save(fileName);
            console.log('✅ PDF generated successfully:', fileName);
            
            return true;
        } catch (error) {
            console.error('❌ Error generating PDF:', error);
            alert('Error generating PDF. Please try again.');
            return false;
        }
    }

    addHeader(teacher) {
        console.log('📄 Adding header section');
        
        // Name (Large, bold)
        this.doc.setFontSize(24);
        this.doc.setFont('helvetica', 'bold');
        this.doc.setTextColor(this.colors.primary);
        this.doc.text(teacher.name || 'Teacher Name', this.margin, this.currentY);
        this.currentY += 10;

        // Designation
        this.doc.setFontSize(16);
        this.doc.setFont('helvetica', 'normal');
        this.doc.setTextColor(this.colors.secondary);
        this.doc.text(teacher.designation || 'Teacher', this.margin, this.currentY);
        this.currentY += 8;

        // Department/School
        this.doc.setFontSize(12);
        this.doc.setTextColor(this.colors.text);
        this.doc.text(teacher.department || 'Academic Department', this.margin, this.currentY);
        this.currentY += this.sectionSpacing;

        // Add a line separator
        this.addSeparatorLine();
    }

    addPersonalSummary(teacher) {
        console.log('📝 Adding personal summary');
        
        this.addSectionHeader('PROFESSIONAL SUMMARY');
        
        // Create a professional summary from available data
        let summary = '';
        if (teacher.total_experience_years) {
            summary += `Experienced educator with ${teacher.total_experience_years} years of professional experience`;
            if (teacher.teaching_experience_years) {
                summary += `, including ${teacher.teaching_experience_years} years in teaching`;
            }
            summary += '. ';
        }
        
        if (teacher.subjects_taught) {
            summary += `Specialized in ${teacher.subjects_taught}. `;
        }
        
        if (teacher.totalAchievements) {
            summary += `Recognized professional with ${teacher.totalAchievements} documented achievements. `;
        }
        
        if (teacher.totalCertifications) {
            summary += `Holds ${teacher.totalCertifications} professional certifications. `;
        }
        
        summary += 'Committed to educational excellence and student development.';
        
        this.addParagraph(summary);
        this.currentY += this.sectionSpacing;
    }

    addContactInformation(teacher) {
        console.log('📞 Adding contact information');
        
        this.addSectionHeader('CONTACT INFORMATION');
        
        const contactInfo = [
            { label: 'Email', value: teacher.email },
            { label: 'Phone', value: teacher.phone },
            { label: 'Employee ID', value: teacher.employee_id },
            { label: 'Address', value: this.formatAddress(teacher) },
            { label: 'Date of Birth', value: this.formatDate(teacher.date_of_birth) },
            { label: 'Gender', value: teacher.gender }
        ];

        contactInfo.forEach(item => {
            if (item.value) {
                this.addContactItem(item.label, item.value);
            }
        });
        
        this.currentY += this.sectionSpacing;
    }

    addProfessionalExperience(teacher) {
        console.log('💼 Adding professional experience');
        
        if (!teacher.experienceTimeline || teacher.experienceTimeline.length === 0) {
            return;
        }

        this.addSectionHeader('PROFESSIONAL EXPERIENCE');
        
        // Sort by date (most recent first)
        const sortedExperience = [...teacher.experienceTimeline].sort((a, b) => {
            if (a.isCurrent && !b.isCurrent) return -1;
            if (!a.isCurrent && b.isCurrent) return 1;
            return (b.year || 0) - (a.year || 0);
        });

        sortedExperience.forEach(exp => {
            this.addExperienceEntry(exp);
        });
        
        this.currentY += this.sectionSpacing;
    }

    addEducation(teacher) {
        console.log('🎓 Adding education');
        
        if (!teacher.educationTimeline || teacher.educationTimeline.length === 0) {
            return;
        }

        this.addSectionHeader('EDUCATION');
        
        // Sort by year (most recent first)
        const sortedEducation = [...teacher.educationTimeline].sort((a, b) => (b.year || 0) - (a.year || 0));

        sortedEducation.forEach(edu => {
            this.addEducationEntry(edu);
        });
        
        this.currentY += this.sectionSpacing;
    }

    addAchievements(teacher) {
        console.log('🏆 Adding achievements');
        
        if (!teacher.achievementsByCategory || Object.keys(teacher.achievementsByCategory).length === 0) {
            return;
        }

        this.addSectionHeader('ACHIEVEMENTS & RECOGNITION');
        
        // Flatten and sort achievements by date (most recent first)
        const allAchievements = [];
        Object.entries(teacher.achievementsByCategory).forEach(([category, achievements]) => {
            achievements.forEach(achievement => {
                allAchievements.push({ ...achievement, category });
            });
        });
        
        allAchievements.sort((a, b) => {
            const dateA = new Date(a.date || '1900-01-01');
            const dateB = new Date(b.date || '1900-01-01');
            return dateB - dateA;
        });

        allAchievements.forEach(achievement => {
            this.addAchievementEntry(achievement);
        });
        
        this.currentY += this.sectionSpacing;
    }

    addSkills(teacher) {
        console.log('🛠️ Adding skills');
        
        if (!teacher.skillsByCategory || Object.keys(teacher.skillsByCategory).length === 0) {
            return;
        }

        this.addSectionHeader('SKILLS & COMPETENCIES');
        
        Object.entries(teacher.skillsByCategory).forEach(([category, skills]) => {
            this.addSkillCategory(category, skills);
        });
        
        this.currentY += this.sectionSpacing;
    }

    addCertifications(teacher) {
        console.log('📜 Adding certifications');
        
        if (!teacher.certifications || teacher.certifications.length === 0) {
            return;
        }

        this.addSectionHeader('PROFESSIONAL CERTIFICATIONS');
        
        // Sort by issue date (most recent first)
        const sortedCertifications = [...teacher.certifications].sort((a, b) => {
            const dateA = new Date(a.issueDate || '1900-01-01');
            const dateB = new Date(b.issueDate || '1900-01-01');
            return dateB - dateA;
        });

        sortedCertifications.forEach(cert => {
            this.addCertificationEntry(cert);
        });
        
        this.currentY += this.sectionSpacing;
    }

    addPublications(teacher) {
        console.log('📚 Adding publications');
        
        // Check for publications data
        const hasPublications = teacher.publications || teacher.research_papers || teacher.conferences_attended;
        
        if (!hasPublications) {
            return;
        }

        this.addSectionHeader('PUBLICATIONS & RESEARCH');
        
        if (teacher.publications) {
            this.addSubsectionHeader('Publications');
            const publications = teacher.publications.split(',');
            publications.forEach(pub => {
                if (pub.trim()) {
                    this.addBulletPoint(pub.trim());
                }
            });
        }
        
        if (teacher.research_papers) {
            this.addSubsectionHeader('Research Papers');
            const papers = teacher.research_papers.split(',');
            papers.forEach(paper => {
                if (paper.trim()) {
                    this.addBulletPoint(paper.trim());
                }
            });
        }
        
        if (teacher.conferences_attended) {
            this.addSubsectionHeader('Conferences');
            const conferences = teacher.conferences_attended.split(',');
            conferences.forEach(conf => {
                if (conf.trim()) {
                    this.addBulletPoint(conf.trim());
                }
            });
        }
        
        this.currentY += this.sectionSpacing;
    }

    addAdditionalInformation(teacher) {
        console.log('ℹ️ Adding additional information');
        
        this.addSectionHeader('ADDITIONAL INFORMATION');
        
        const additionalInfo = [
            { label: 'Employment Type', value: teacher.employment_type },
            { label: 'Joining Date', value: this.formatDate(teacher.joining_date) },
            { label: 'Confirmation Date', value: this.formatDate(teacher.confirmation_date) },
            { label: 'Performance Rating', value: teacher.performance_rating },
            { label: 'Office Location', value: teacher.office_location },
            { label: 'Languages Known', value: teacher.languages_known },
            { label: 'Account Status', value: teacher.account_status }
        ];

        additionalInfo.forEach(item => {
            if (item.value) {
                this.addInfoItem(item.label, item.value);
            }
        });
        
        // Add notes if available
        if (teacher.combinedNotes) {
            this.addSubsectionHeader('Notes');
            this.addParagraph(teacher.combinedNotes);
        }
    }

    // Helper methods for formatting and layout
    addSectionHeader(title) {
        this.checkPageBreak(15);
        
        this.doc.setFontSize(14);
        this.doc.setFont('helvetica', 'bold');
        this.doc.setTextColor(this.colors.primary);
        this.doc.text(title, this.margin, this.currentY);
        this.currentY += 8;
        
        // Add underline
        this.doc.setDrawColor(this.colors.primary);
        this.doc.setLineWidth(0.5);
        this.doc.line(this.margin, this.currentY - 2, this.pageWidth - this.margin, this.currentY - 2);
        this.currentY += 3;
    }

    addSubsectionHeader(title) {
        this.checkPageBreak(10);
        
        this.doc.setFontSize(12);
        this.doc.setFont('helvetica', 'bold');
        this.doc.setTextColor(this.colors.accent);
        this.doc.text(title, this.margin, this.currentY);
        this.currentY += 6;
    }

    addExperienceEntry(exp) {
        this.checkPageBreak(20);
        
        // Job title and organization
        this.doc.setFontSize(12);
        this.doc.setFont('helvetica', 'bold');
        this.doc.setTextColor(this.colors.text);
        this.doc.text(exp.title || 'Position', this.margin, this.currentY);
        this.currentY += 5;
        
        // Organization and duration
        this.doc.setFontSize(10);
        this.doc.setFont('helvetica', 'normal');
        this.doc.setTextColor(this.colors.secondary);
        const orgText = `${exp.institution || 'Organization'} | ${exp.duration || exp.year || 'Duration'}`;
        this.doc.text(orgText, this.margin, this.currentY);
        this.currentY += 5;
        
        // Description
        if (exp.description) {
            this.addParagraph(exp.description, this.margin + 5);
        }
        
        // Responsibilities
        if (exp.responsibilities && exp.responsibilities.length > 0) {
            exp.responsibilities.slice(0, 3).forEach(resp => {
                this.addBulletPoint(resp, this.margin + 5);
            });
        }
        
        // Achievements
        if (exp.achievements && exp.achievements.length > 0) {
            exp.achievements.slice(0, 2).forEach(achievement => {
                this.addBulletPoint(`Achievement: ${achievement}`, this.margin + 5);
            });
        }
        
        this.currentY += 4;
    }

    addEducationEntry(edu) {
        this.checkPageBreak(15);
        
        // Degree and institution
        this.doc.setFontSize(12);
        this.doc.setFont('helvetica', 'bold');
        this.doc.setTextColor(this.colors.text);
        this.doc.text(edu.title || 'Degree', this.margin, this.currentY);
        this.currentY += 5;
        
        // Institution and year
        this.doc.setFontSize(10);
        this.doc.setFont('helvetica', 'normal');
        this.doc.setTextColor(this.colors.secondary);
        const eduText = `${edu.institution || 'Institution'} | ${edu.year || 'Year'}`;
        this.doc.text(eduText, this.margin, this.currentY);
        this.currentY += 5;
        
        // Performance details
        const details = [];
        if (edu.percentage) details.push(`${edu.percentage}%`);
        if (edu.grade) details.push(`Grade: ${edu.grade}`);
        if (edu.cgpa) details.push(`CGPA: ${edu.cgpa}`);
        
        if (details.length > 0) {
            this.doc.text(details.join(' | '), this.margin + 5, this.currentY);
            this.currentY += 4;
        }
        
        this.currentY += 3;
    }

    addAchievementEntry(achievement) {
        this.checkPageBreak(12);
        
        // Achievement title
        this.doc.setFontSize(11);
        this.doc.setFont('helvetica', 'bold');
        this.doc.setTextColor(this.colors.text);
        this.doc.text(achievement.title, this.margin, this.currentY);
        this.currentY += 4;
        
        // Date and level
        this.doc.setFontSize(9);
        this.doc.setFont('helvetica', 'normal');
        this.doc.setTextColor(this.colors.secondary);
        const achievementInfo = `${this.formatDate(achievement.date)} | ${achievement.level} Level`;
        this.doc.text(achievementInfo, this.margin, this.currentY);
        this.currentY += 4;
        
        // Description
        if (achievement.description) {
            this.addParagraph(achievement.description, this.margin + 3, 9);
        }
        
        this.currentY += 3;
    }

    addSkillCategory(category, skills) {
        this.checkPageBreak(8);
        
        // Category name
        this.doc.setFontSize(11);
        this.doc.setFont('helvetica', 'bold');
        this.doc.setTextColor(this.colors.accent);
        this.doc.text(category.replace('_', ' ').toUpperCase(), this.margin, this.currentY);
        this.currentY += 5;
        
        // Skills list
        const skillNames = skills.map(skill => skill.name).join(', ');
        this.addParagraph(skillNames, this.margin + 5, 10);
        this.currentY += 3;
    }

    addCertificationEntry(cert) {
        this.checkPageBreak(10);
        
        // Certification name
        this.doc.setFontSize(11);
        this.doc.setFont('helvetica', 'bold');
        this.doc.setTextColor(this.colors.text);
        this.doc.text(cert.name, this.margin, this.currentY);
        this.currentY += 4;
        
        // Issuer and date
        this.doc.setFontSize(9);
        this.doc.setFont('helvetica', 'normal');
        this.doc.setTextColor(this.colors.secondary);
        const certInfo = `${cert.issuer} | Issued: ${this.formatDate(cert.issueDate)}`;
        this.doc.text(certInfo, this.margin, this.currentY);
        this.currentY += 4;
        
        this.currentY += 2;
    }

    addContactItem(label, value) {
        this.checkPageBreak(5);
        
        this.doc.setFontSize(10);
        this.doc.setFont('helvetica', 'bold');
        this.doc.setTextColor(this.colors.text);
        this.doc.text(`${label}:`, this.margin, this.currentY);
        
        this.doc.setFont('helvetica', 'normal');
        this.doc.setTextColor(this.colors.secondary);
        this.doc.text(value, this.margin + 30, this.currentY);
        this.currentY += 4;
    }

    addInfoItem(label, value) {
        this.checkPageBreak(5);
        
        this.doc.setFontSize(9);
        this.doc.setFont('helvetica', 'bold');
        this.doc.setTextColor(this.colors.text);
        this.doc.text(`${label}:`, this.margin, this.currentY);
        
        this.doc.setFont('helvetica', 'normal');
        this.doc.setTextColor(this.colors.secondary);
        this.doc.text(value, this.margin + 35, this.currentY);
        this.currentY += 4;
    }

    addParagraph(text, leftMargin = null, fontSize = 10) {
        if (!text) return;
        
        const margin = leftMargin || this.margin;
        const maxWidth = this.pageWidth - margin - this.margin;
        
        this.doc.setFontSize(fontSize);
        this.doc.setFont('helvetica', 'normal');
        this.doc.setTextColor(this.colors.text);
        
        const lines = this.doc.splitTextToSize(text, maxWidth);
        
        lines.forEach(line => {
            this.checkPageBreak(5);
            this.doc.text(line, margin, this.currentY);
            this.currentY += 4;
        });
    }

    addBulletPoint(text, leftMargin = null) {
        if (!text) return;
        
        const margin = leftMargin || this.margin;
        this.checkPageBreak(5);
        
        this.doc.setFontSize(9);
        this.doc.setFont('helvetica', 'normal');
        this.doc.setTextColor(this.colors.text);
        
        this.doc.text('•', margin, this.currentY);
        
        const maxWidth = this.pageWidth - margin - this.margin - 5;
        const lines = this.doc.splitTextToSize(text, maxWidth);
        
        lines.forEach((line, index) => {
            if (index > 0) this.checkPageBreak(4);
            this.doc.text(line, margin + 5, this.currentY);
            if (index < lines.length - 1) this.currentY += 4;
        });
        
        this.currentY += 4;
    }

    addSeparatorLine() {
        this.doc.setDrawColor(this.colors.secondary);
        this.doc.setLineWidth(0.2);
        this.doc.line(this.margin, this.currentY, this.pageWidth - this.margin, this.currentY);
        this.currentY += 5;
    }

    checkPageBreak(requiredSpace) {
        if (this.currentY + requiredSpace > this.pageHeight - this.margin) {
            this.doc.addPage();
            this.currentY = this.margin;
        }
    }

    formatDate(dateString) {
        if (!dateString) return '';
        try {
            return new Date(dateString).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        } catch {
            return dateString;
        }
    }

    formatAddress(teacher) {
        const parts = [
            teacher.address,
            teacher.city,
            teacher.state,
            teacher.pincode
        ].filter(Boolean);
        
        return parts.join(', ');
    }
}

// Global function to generate PDF
window.generateTeacherProfilePDF = function(teacherData) {
    console.log('🔄 Generating teacher profile PDF...');
    const generator = new TeacherProfilePDFGenerator();
    return generator.generatePDF(teacherData);
};

console.log('✅ Teacher Profile PDF Generator loaded');
