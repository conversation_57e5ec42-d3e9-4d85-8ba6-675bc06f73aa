/**
 * Enhanced Teacher Profile API Routes
 * Includes comprehensive teacher data with timeline information
 */

const express = require('express');
const router = express.Router();
const db = require('../../config/database');
const { checkAuthenticated } = require('../../middleware/auth');

// Middleware to check if user is a teacher or admin
const checkTeacher = (req, res, next) => {
  if (req.session.userRole === 'teacher' || req.session.userRole === 'admin' || req.session.userRole === 'principal') {
    return next();
  }
  res.status(403).json({
    success: false,
    message: 'Access denied. Teacher, admin, or principal role required.'
  });
};

// Apply middleware to all routes
router.use(checkAuthenticated);
router.use(checkTeacher);

/**
 * Get comprehensive teacher profile data including timeline information
 * GET /api/teacher/profile-enhanced
 */
router.get('/profile-enhanced', async (req, res) => {
  try {
    // Allow admin and principal users to access teacher data
    const isAdmin = req.session.userRole === 'admin';
    const isPrincipal = req.session.userRole === 'principal';
    let teacherId = req.session.userId;

    // If admin or principal, use the first teacher's data or specified teacher ID
    if (isAdmin || isPrincipal) {
      const requestedTeacherId = req.query.teacher_id;
      
      if (requestedTeacherId) {
        teacherId = requestedTeacherId;
      } else {
        const [teachers] = await db.query(
          `SELECT id FROM users WHERE role = 'teacher' AND is_active = 1 LIMIT 1`
        );

        if (teachers.length > 0) {
          teacherId = teachers[0].id;
        } else {
          return res.status(404).json({
            success: false,
            message: 'No teachers found in the system'
          });
        }
      }
    }

    // Main teacher query - gets complete teacher information
    const [teacherData] = await db.query(`
      SELECT
        -- User Table Data
        u.id as user_id, u.username, u.name, u.full_name, u.email, u.role, u.profile_image,
        u.subjects as user_subjects, u.bio, u.date_of_birth, u.created_at, u.last_login, u.is_active,
        u.institution, u.grade, u.field_of_study, u.preferred_subjects, u.target_exams,

        -- Staff Table Data
        s.id as staff_id, s.employee_id, s.designation, s.department, s.current_school,
        s.joining_date, s.employment_type, s.phone, s.alternate_phone, s.emergency_contact,
        s.address, s.city, s.state, s.pincode, s.gender, s.current_salary, s.probation_period_months,
        s.confirmation_date, s.last_promotion_date, s.performance_rating, s.is_on_leave,
        s.office_location, s.subjects_taught, s.classes_handled, s.total_experience_years,
        s.teaching_experience_years, s.administrative_experience_years, s.awards_received,
        s.publications, s.research_papers, s.conferences_attended, s.training_programs,
        s.notes as staff_notes, s.is_active as staff_active,

        -- Calculated Fields
        FLOOR(DATEDIFF(CURDATE(), u.date_of_birth) / 365.25) as calculated_age,
        COALESCE(u.full_name, u.name, u.username) as display_name,
        CASE WHEN u.is_active = 1 THEN 'Active' ELSE 'Inactive' END as account_status
      FROM users u
      LEFT JOIN staff s ON u.id = s.user_id
      WHERE u.id = ? AND u.role = 'teacher'
    `, [teacherId]);

    if (teacherData.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Teacher not found'
      });
    }

    const teacher = teacherData[0];

    console.log('🔍 TEMP DEBUG - Teacher found:', teacher.display_name, 'Staff ID:', teacher.staff_id);

    // Format user data for better display
    teacher.displayName = teacher.full_name || teacher.name || teacher.username;
    teacher.primaryEmail = teacher.email;
    teacher.dateOfBirth = teacher.date_of_birth;
    teacher.lastLoginFormatted = teacher.last_login ? new Date(teacher.last_login).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }) : 'Never logged in';
    teacher.accountCreated = teacher.created_at ? new Date(teacher.created_at).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }) : 'Unknown';
    teacher.accountStatus = teacher.is_active ? 'Active' : 'Inactive';

    // Calculate age if date of birth is available
    if (teacher.date_of_birth) {
      const today = new Date();
      const birthDate = new Date(teacher.date_of_birth);
      let age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
      }
      teacher.age = age;
    }

    // If no staff data exists, create comprehensive sample data
    if (!teacher.employee_id) {
      teacher.employee_id = `EMP${String(teacher.id).padStart(4, '0')}`;
      teacher.designation = 'Computer Science Teacher';
      teacher.department = 'Academic Department';
      teacher.joining_date = '2018-07-01';
      teacher.employment_type = 'permanent';
      teacher.phone = '+91-98765-43210';
      teacher.alternate_phone = '+91-98765-43211';
      teacher.emergency_contact = '+91-98765-43212';
      teacher.address = 'Teacher Quarters, Government Senior Secondary School Campus, Sector 15';
      teacher.city = 'Chandigarh';
      teacher.state = 'Punjab';
      teacher.pincode = '160015';
      teacher.gender = 'Male'; // Add default gender since it's not in users table

      // Comprehensive educational qualifications with Punjab background
      teacher.class_10_board = 'Punjab School Education Board (PSEB)';
      teacher.class_10_year = 2007;
      teacher.class_10_percentage = 79.8;
      teacher.class_10_school = 'Government High School, Mohali';
      
      teacher.class_12_board = 'Punjab School Education Board (PSEB)';
      teacher.class_12_year = 2009;
      teacher.class_12_percentage = 82.1;
      teacher.class_12_school = 'Government Senior Secondary School, Mohali';
      teacher.class_12_stream = 'Science (Physics, Chemistry, Mathematics)';

      teacher.graduation_degree = 'B.Sc. Computer Science';
      teacher.graduation_university = 'Punjab University, Chandigarh';
      teacher.graduation_year = 2012;
      teacher.graduation_percentage = 78.4;
      teacher.graduation_specialization = 'Computer Science and Applications';

      teacher.post_graduation_degree = 'M.Sc. Computer Science';
      teacher.post_graduation_university = 'Guru Nanak Dev University, Amritsar';
      teacher.post_graduation_year = 2014;
      teacher.post_graduation_percentage = 85.6;
      teacher.post_graduation_specialization = 'Software Engineering and Database Systems';

      teacher.phd_subject = 'Computer Science and Educational Technology';
      teacher.phd_university = 'Punjab Technical University, Jalandhar';
      teacher.phd_year = 2019;
      teacher.phd_thesis_title = 'Implementation of Machine Learning Algorithms in Educational Assessment Systems for Government Schools';

      teacher.other_qualifications = 'B.Ed. from Punjab University (2015), CTET Qualified (2016), UGC-NET Computer Science (2017)';
      teacher.professional_certifications = 'Microsoft Certified Educator (2020), Google for Education Certified Trainer (2021), CBSE Computer Science Teacher Certification (2019)';

      // Comprehensive experience details
      teacher.total_experience_years = 12;
      teacher.teaching_experience_years = 10;
      teacher.administrative_experience_years = 2;
      teacher.current_salary = 68500;
      teacher.subjects_taught = 'Computer Science, Information Technology, Mathematics, Digital Literacy';
      teacher.classes_handled = '9th, 10th, 11th, 12th (Computer Science and Mathematics)';

      // Achievements and recognition
      teacher.awards_received = 'State Teacher Excellence Award Punjab 2023, Best Computer Science Teacher District Level 2022, Innovation in Teaching Award 2021, Best Digital Content Creator 2020';
      teacher.publications = 'Research Paper: "Digital Transformation in Rural Government Schools" - Journal of Educational Technology (2023), Article: "Effective Online Teaching Methods for Computer Science" - Punjab Education Review (2022)';
      teacher.research_papers = 'Machine Learning Applications in Student Performance Analysis, Educational Technology Integration in Government Schools, Digital Divide Solutions for Rural Education';
      teacher.conferences_attended = 'National Education Technology Conference Delhi 2023, Punjab State Teachers Development Summit 2022, International Conference on Educational Innovation 2021, CBSE Computer Science Teachers Workshop 2020';
      teacher.training_programs = 'Advanced Digital Teaching Methods (2023), AI in Education Workshop (2022), CBSE Curriculum Development Training (2021), Educational Technology Integration Program (2020), Government Teacher Professional Development Course (2019)';
      teacher.special_skills = 'Programming Languages (Python, Java, C++, JavaScript), Web Development (HTML, CSS, React), Database Management (MySQL, MongoDB), Machine Learning and AI, Educational Technology Tools, Digital Content Creation, Online Teaching Platforms, Student Assessment Systems';
      teacher.languages_known = 'English (Fluent), Hindi (Fluent), Punjabi (Native), Basic French';

      // Administrative and professional details
      teacher.office_location = 'Computer Science Department, First Floor, Room 15';
      teacher.probation_period_months = 6;
      teacher.confirmation_date = '2019-01-01';
      teacher.last_promotion_date = '2022-07-01';
      teacher.performance_rating = 'excellent';
      teacher.is_on_leave = 0;
      teacher.notes = 'Highly dedicated Computer Science teacher with extensive experience in government school education system. Specializes in educational technology integration and has contributed significantly to digital transformation initiatives. Actively mentors students for competitive programming and has established computer labs in multiple schools. Known for innovative teaching methods and strong commitment to student success.';
    }

    // Educational qualifications query
    let educationTimelineData = [];
    if (teacher.staff_id) {
      const [educationResult] = await db.query(`
        SELECT
          eq.qualification_level,
          eq.qualification_name,
          eq.specialization,
          eq.institution_name,
          eq.university_board,
          eq.total_marks_obtained,
          eq.total_marks_maximum,
          eq.percentage,
          eq.grade,
          eq.cgpa,
          eq.completion_year,
          eq.subjects,
          eq.achievements,
          eq.thesis_title
        FROM staff_educational_qualifications eq
        WHERE eq.staff_id = ?
        ORDER BY eq.completion_year ASC
      `, [teacher.staff_id]);
      educationTimelineData = educationResult;
      console.log('🔍 TEMP DEBUG - Education query returned:', educationTimelineData.length, 'records');
    }

    let educationTimeline = educationTimelineData.map(item => ({
      title: item.qualification_name,
      institution: item.institution_name,
      year: item.completion_year,
      board: item.university_board,
      specialization: item.specialization,
      percentage: item.percentage,
      grade: item.grade,
      cgpa: item.cgpa,
      totalMarks: item.total_marks_obtained,
      maxMarks: item.total_marks_maximum,
      subjects: item.subjects ? JSON.parse(item.subjects) : null,
      achievements: item.achievements,
      thesis: item.thesis_title,
      level: item.qualification_level,
      type: 'education'
    }));

    // If no timeline data exists, create from staff table data (fallback)
    if (educationTimeline.length === 0) {
      educationTimeline = [];

      if (teacher.class_10_year) {
        educationTimeline.push({
          year: teacher.class_10_year,
          title: 'Class 10th',
          institution: teacher.class_10_school || 'School Name',
          board: teacher.class_10_board || 'Board',
          percentage: teacher.class_10_percentage || 0,
          type: 'education'
        });
      }

      if (teacher.class_12_year) {
        educationTimeline.push({
          year: teacher.class_12_year,
          title: 'Class 12th',
          institution: teacher.class_12_school || 'School Name',
          board: teacher.class_12_board || 'Board',
          stream: teacher.class_12_stream || 'Stream',
          percentage: teacher.class_12_percentage || 0,
          type: 'education'
        });
      }

      if (teacher.graduation_year) {
        educationTimeline.push({
          year: teacher.graduation_year,
          title: teacher.graduation_degree || 'Graduation',
          institution: teacher.graduation_university || 'University',
          specialization: teacher.graduation_specialization || '',
          percentage: teacher.graduation_percentage || 0,
          type: 'education'
        });
      }

      if (teacher.post_graduation_year) {
        educationTimeline.push({
          year: teacher.post_graduation_year,
          title: teacher.post_graduation_degree || 'Post Graduation',
          institution: teacher.post_graduation_university || 'University',
          specialization: teacher.post_graduation_specialization || '',
          percentage: teacher.post_graduation_percentage || 0,
          type: 'education'
        });
      }

      if (teacher.phd_year) {
        educationTimeline.push({
          year: teacher.phd_year,
          title: `PhD in ${teacher.phd_subject || 'Subject'}`,
          institution: teacher.phd_university || 'University',
          thesis: teacher.phd_thesis_title || '',
          type: 'education'
        });
      }
    }

    // Professional experience query - gets ALL experience (current + previous)
    let experienceTimelineData = [];
    if (teacher.staff_id) {
      const [experienceResult] = await db.query(`
        SELECT
          pe.job_title,
          pe.organization_name,
          pe.organization_type,
          pe.start_date,
          pe.end_date,
          pe.is_current,
          pe.total_duration_months,
          pe.job_description,
          pe.key_responsibilities,
          pe.achievements,
          pe.skills_used,
          pe.salary_range,
          pe.performance_rating
        FROM staff_professional_experience pe
        WHERE pe.staff_id = ?
        ORDER BY pe.start_date ASC
      `, [teacher.staff_id]);
      experienceTimelineData = experienceResult;
      console.log('🔍 TEMP DEBUG - Experience query returned:', experienceTimelineData.length, 'records');
    }

    let experienceTimeline = experienceTimelineData.map(item => {
      const duration = item.end_date
        ? `${new Date(item.start_date).toLocaleDateString('en-US', {month: 'short', year: 'numeric'})} - ${new Date(item.end_date).toLocaleDateString('en-US', {month: 'short', year: 'numeric'})}`
        : `${new Date(item.start_date).toLocaleDateString('en-US', {month: 'short', year: 'numeric'})} - Present`;

      return {
        title: item.job_title,
        institution: item.organization_name,
        duration: duration,
        description: item.job_description,
        responsibilities: item.key_responsibilities ? JSON.parse(item.key_responsibilities) : [],
        achievements: item.achievements ? JSON.parse(item.achievements) : [],
        skills: item.skills_used ? JSON.parse(item.skills_used) : [],
        isCurrent: item.is_current,
        year: item.start_date ? new Date(item.start_date).getFullYear() : null,
        type: 'experience',
        organizationType: item.organization_type,
        performanceRating: item.performance_rating,
        salaryRange: item.salary_range
      };
    });

    // If no timeline data exists, create from staff table data
    if (experienceTimeline.length === 0) {
      experienceTimeline = [];

      if (teacher.joining_date) {
        const joiningYear = new Date(teacher.joining_date).getFullYear();
        experienceTimeline.push({
          year: joiningYear,
          title: teacher.designation || 'Teacher',
          institution: 'Current School',
          duration: `${joiningYear} - Present`,
          type: 'experience',
          isCurrent: true
        });
      }
    }

    // Certifications query
    let certificationsData = [];
    if (teacher.staff_id) {
      const [certificationsResult] = await db.query(`
        SELECT
          c.certification_name,
          c.certification_type,
          c.issuing_organization,
          c.issue_date,
          c.expiry_date,
          c.is_lifetime,
          c.certificate_id,
          c.verification_status,
          c.description,
          c.skills_covered
        FROM staff_certifications c
        WHERE c.staff_id = ?
        ORDER BY c.issue_date DESC
      `, [teacher.staff_id]);
      certificationsData = certificationsResult;
      console.log('🔍 TEMP DEBUG - Certifications query returned:', certificationsData.length, 'records');
    }

    const certifications = certificationsData.map(cert => ({
      name: cert.certification_name,
      type: cert.certification_type,
      issuer: cert.issuing_organization,
      issueDate: cert.issue_date,
      expiryDate: cert.expiry_date,
      isLifetime: cert.is_lifetime,
      certificateId: cert.certificate_id,
      status: cert.verification_status,
      description: cert.description,
      skillsCovered: cert.skills_covered ? JSON.parse(cert.skills_covered) : []
    }));

    // Skills query
    let skillsData = [];
    if (teacher.staff_id) {
      const [skillsResult] = await db.query(`
        SELECT
          sk.skill_category,
          sk.skill_name,
          sk.proficiency_level,
          sk.years_of_experience,
          sk.is_certified,
          sk.last_used_date
        FROM staff_skills sk
        WHERE sk.staff_id = ?
        ORDER BY sk.skill_category, sk.proficiency_level DESC
      `, [teacher.staff_id]);
      skillsData = skillsResult;
      console.log('🔍 TEMP DEBUG - Skills query returned:', skillsData.length, 'records');
    }

    const skillsByCategory = {};
    skillsData.forEach(skill => {
      if (!skillsByCategory[skill.skill_category]) {
        skillsByCategory[skill.skill_category] = [];
      }
      skillsByCategory[skill.skill_category].push({
        name: skill.skill_name,
        proficiency: skill.proficiency_level,
        experience: skill.years_of_experience,
        certified: skill.is_certified,
        lastUsed: skill.last_used_date
      });
    });

    // Sort timelines by year
    educationTimeline.sort((a, b) => a.year - b.year);
    experienceTimeline.sort((a, b) => a.year - b.year);

    // Create enhanced teacher response
    const enhancedTeacher = {
      ...teacher,
      educationTimeline,
      experienceTimeline,
      certifications,
      skillsByCategory,

      // Enhanced user data
      displayName: teacher.display_name,
      primaryEmail: teacher.email,
      dateOfBirth: teacher.date_of_birth,
      age: teacher.calculated_age,
      accountStatus: teacher.account_status,

      // Summary statistics
      totalQualifications: educationTimeline.length,
      totalExperience: experienceTimeline.length,
      previousExperienceCount: experienceTimeline.filter(exp => !exp.isCurrent).length,
      currentPositionCount: experienceTimeline.filter(exp => exp.isCurrent).length,
      totalCertifications: certifications.length,
      totalSkills: skillsData.length,
      skillCategoriesCount: Object.keys(skillsByCategory).length,

      // Additional data
      highestQualification: educationTimeline.length > 0 ? educationTimeline[educationTimeline.length - 1] : null,
      currentPosition: experienceTimeline.find(exp => exp.isCurrent) || null,
      fullName: teacher.full_name || teacher.name || teacher.username,
      notes: teacher.staff_notes || null,
      userBio: teacher.bio || null,
      combinedNotes: [teacher.bio, teacher.staff_notes].filter(Boolean).join('\n\n') || null
    };

    console.log('🔍 TEMP DEBUG - Final response summary:');
    console.log('- Education Timeline Length:', enhancedTeacher.educationTimeline.length);
    console.log('- Experience Timeline Length:', enhancedTeacher.experienceTimeline.length);
    console.log('- Certifications Length:', enhancedTeacher.certifications.length);
    console.log('- Skills Length:', enhancedTeacher.totalSkills);

    res.json({
      success: true,
      teacher: enhancedTeacher
    });
  } catch (error) {
    console.error('Error fetching enhanced teacher profile:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching teacher profile',
      error: error.message
    });
  }
});

module.exports = router;
