<!-- Page-specific styles for teacher management -->
<style>
    .teacher-row {
        transition: all 0.2s ease;
    }
    .teacher-row:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
</style>

<!-- Teacher Management Overview -->
<div class="mb-8">
    <!-- Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-white rounded-lg shadow-md p-6 card-hover">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-principal-light text-principal-primary">
                    <i class="fas fa-users text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Teachers</p>
                    <p class="text-2xl font-bold text-gray-900"><%= teachers.length %></p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6 card-hover">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100 text-green-600">
                    <i class="fas fa-check-circle text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">High Performers</p>
                    <p class="text-2xl font-bold text-gray-900">
                        <%= teachers.filter(t => (t.completion_rate || 0) >= 80).length %>
                    </p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6 card-hover">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                    <i class="fas fa-exclamation-triangle text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Need Attention</p>
                    <p class="text-2xl font-bold text-gray-900">
                        <%= teachers.filter(t => (t.completion_rate || 0) < 60).length %>
                    </p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6 card-hover">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-red-100 text-red-600">
                    <i class="fas fa-clock text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Overdue Tasks</p>
                    <p class="text-2xl font-bold text-gray-900">
                        <%= teachers.reduce((sum, t) => sum + (t.overdue_lectures || 0), 0) %>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Teacher Performance Table -->
<div class="bg-white rounded-lg shadow-md">
    <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h2 class="text-xl font-bold text-gray-900">Teacher Performance Dashboard</h2>
            <div class="flex items-center space-x-4">
                <!-- Export Button -->
                <button class="btn-principal-outline px-4 py-2 rounded-lg text-sm font-medium">
                    <i class="fas fa-download mr-2"></i>
                    Export Report
                </button>
                <!-- Refresh Button -->
                <button onclick="refreshTeacherData()" class="btn-principal px-4 py-2 rounded-lg text-sm font-medium">
                    <i class="fas fa-sync-alt mr-2"></i>
                    Refresh
                </button>
            </div>
        </div>
    </div>

    <div class="p-6">
        <!-- Search and Filter -->
        <div class="mb-6 flex flex-col sm:flex-row gap-4">
            <div class="flex-1">
                <input type="text" id="search-teachers" placeholder="Search teachers by name or email..."
                       class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-principal-primary focus:border-transparent">
            </div>
            <div class="flex gap-2">
                <select id="filter-performance" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-principal-primary focus:border-transparent">
                    <option value="">All Performance</option>
                    <option value="excellent">Excellent (80%+)</option>
                    <option value="good">Good (60-79%)</option>
                    <option value="average">Average (40-59%)</option>
                    <option value="poor">Poor (<40%)</option>
                </select>
            </div>
        </div>

        <% if (teachers && teachers.length > 0) { %>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 table-principal">
                    <thead>
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Teacher
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Lectures
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Completion Rate
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Overdue
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Last Login
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Performance
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200" id="teachers-table-body">
                        <% teachers.forEach(teacher => { %>
                            <%
                                const completionRate = teacher.completion_rate || 0;
                                const performanceLevel = completionRate >= 80 ? 'excellent' : completionRate >= 60 ? 'good' : completionRate >= 40 ? 'average' : 'poor';
                            %>
                            <tr class="hover:bg-gray-50 teacher-row"
                                data-name="<%= teacher.name.toLowerCase() %>"
                                data-email="<%= teacher.email.toLowerCase() %>"
                                data-performance="<%= performanceLevel %>">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            <div class="h-10 w-10 rounded-full bg-principal-light flex items-center justify-center">
                                                <span class="text-sm font-medium text-principal-primary">
                                                    <%= teacher.name.split(' ').map(n => n[0]).join('').toUpperCase() %>
                                                </span>
                                            </div>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">
                                                <%= teacher.name %>
                                            </div>
                                            <div class="text-sm text-gray-500">
                                                <%= teacher.email %>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">
                                        <span class="font-medium"><%= teacher.delivered_lectures || 0 %></span>/<%= teacher.total_lectures || 0 %>
                                    </div>
                                    <div class="text-xs text-gray-500">
                                        Total lectures
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-16 bg-gray-200 rounded-full h-2 mr-3">
                                            <div class="h-2 rounded-full <%= completionRate >= 80 ? 'bg-green-500' : completionRate >= 60 ? 'bg-blue-500' : completionRate >= 40 ? 'bg-yellow-500' : 'bg-red-500' %>"
                                                 style="width: <%= Math.min(completionRate, 100) %>%"></div>
                                        </div>
                                        <span class="text-sm font-medium text-gray-900">
                                            <%= parseFloat(completionRate || 0).toFixed(1) %>%
                                        </span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <% if (teacher.overdue_lectures > 0) { %>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            <i class="fas fa-exclamation-triangle mr-1"></i>
                                            <%= teacher.overdue_lectures %>
                                        </span>
                                    <% } else { %>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            <i class="fas fa-check mr-1"></i>
                                            Up to date
                                        </span>
                                    <% } %>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <% if (teacher.last_login) { %>
                                        <%= new Date(teacher.last_login).toLocaleDateString() %>
                                    <% } else { %>
                                        Never
                                    <% } %>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= completionRate >= 80 ? 'bg-green-100 text-green-800' : completionRate >= 60 ? 'bg-blue-100 text-blue-800' : completionRate >= 40 ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800' %>">
                                        <% if (completionRate >= 80) { %>
                                            <i class="fas fa-star mr-1"></i> Excellent
                                        <% } else if (completionRate >= 60) { %>
                                            <i class="fas fa-thumbs-up mr-1"></i> Good
                                        <% } else if (completionRate >= 40) { %>
                                            <i class="fas fa-minus-circle mr-1"></i> Average
                                        <% } else { %>
                                            <i class="fas fa-times-circle mr-1"></i> Poor
                                        <% } %>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <button id="viewTeacherBtn-<%= teacher.id %>"
                                                class="view-teacher-btn text-principal-primary hover:text-principal-dark p-1 rounded"
                                                data-teacher-id="<%= teacher.id %>"
                                                title="View Teacher Details">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button onclick="sendMessage(<%= teacher.id %>)"
                                                class="text-blue-600 hover:text-blue-900 p-1 rounded"
                                                title="Send Message">
                                            <i class="fas fa-envelope"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <% }); %>
                    </tbody>
                </table>
            </div>
        <% } else { %>
            <div class="text-center py-12">
                <i class="fas fa-user-friends text-4xl text-gray-300 mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900">No teachers found</h3>
                <p class="text-sm text-gray-500">Teacher data will appear here once teachers are added to the system.</p>
            </div>
        <% } %>
    </div>
</div>

<!-- Enhanced Teacher Details Modal -->
<div id="teacherModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl max-w-[95vw] w-full max-h-[95vh] overflow-y-auto mx-2">
    <!-- Modal Header -->
    <div class="bg-gradient-to-r from-principal-primary to-principal-secondary text-white px-6 py-4 rounded-t-lg flex justify-between items-center">
      <h3 id="teacherModalTitle" class="text-xl font-semibold flex items-center">
        <i class="fas fa-user-tie mr-3"></i>
        Enhanced Teacher Profile
      </h3>
      <button id="closeTeacherModalBtn" class="text-white hover:text-gray-200">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>

    <!-- Modal Content -->
    <div id="teacherModalContent" class="p-6">
      <!-- Loading state -->
      <div id="modal-loading" class="flex items-center justify-center py-12">
        <i class="fas fa-spinner fa-spin text-3xl text-principal-primary mr-3"></i>
        <span class="text-lg text-gray-600">Loading teacher profile...</span>
      </div>

      <!-- Enhanced Profile Content (will be populated) -->
      <div id="enhanced-profile-content" class="hidden">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <!-- Personal Information Card -->
          <div class="lg:col-span-1">
            <div class="bg-white rounded-lg border shadow-sm overflow-hidden">
              <div class="bg-gradient-to-r from-blue-600 to-blue-700 text-white p-4">
                <h3 class="text-lg font-semibold">Personal Information</h3>
              </div>
              <div class="p-4">
                <!-- Profile Image -->
                <div class="flex flex-col items-center mb-4">
                  <div class="relative">
                    <div id="modal-profile-image-container" class="w-24 h-24 rounded-full bg-blue-600 border-4 border-blue-600 shadow-lg overflow-hidden flex items-center justify-center">
                      <div id="modal-profile-image-placeholder" class="text-2xl font-bold text-white">T</div>
                      <img id="modal-profile-image" class="w-full h-full object-cover hidden" src="" alt="Profile Image">
                    </div>
                  </div>
                  <h4 id="modal-teacher-name" class="text-lg font-bold text-gray-800 text-center">Loading...</h4>
                  <p id="modal-teacher-designation" class="text-blue-600 font-semibold text-center">Teacher</p>
                  <span id="modal-teacher-department" class="mt-2 px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
                    Academic Department
                  </span>
                </div>

                <!-- Basic Information -->
                <div class="space-y-3 text-sm">
                  <div class="flex items-center">
                    <i class="fas fa-id-badge text-blue-600 w-5"></i>
                    <span id="modal-teacher-employee-id" class="ml-3 text-gray-700">EMP0001</span>
                  </div>
                  <div class="flex items-center">
                    <i class="fas fa-envelope text-blue-600 w-5"></i>
                    <span id="modal-teacher-email" class="ml-3 text-gray-700"><EMAIL></span>
                  </div>
                  <div class="flex items-center">
                    <i class="fas fa-phone text-blue-600 w-5"></i>
                    <span id="modal-teacher-phone" class="ml-3 text-gray-700">+91-XXXXXXXXXX</span>
                  </div>
                  <div class="flex items-center">
                    <i class="fas fa-calendar text-blue-600 w-5"></i>
                    <span id="modal-teacher-joining-date" class="ml-3 text-gray-700">Joining Date</span>
                  </div>
                  <div class="flex items-center">
                    <i class="fas fa-briefcase text-blue-600 w-5"></i>
                    <span id="modal-teacher-employment-type" class="ml-3 text-gray-700">Employment Type</span>
                  </div>
                </div>

                <!-- Additional Personal Details -->
                <div class="mt-4 pt-4 border-t border-gray-200">
                  <h5 class="font-semibold text-gray-700 mb-3 text-sm">Additional Details</h5>
                  <div class="space-y-2 text-sm">
                    <div class="flex items-center">
                      <i class="fas fa-birthday-cake text-blue-600 w-5"></i>
                      <span id="modal-date-of-birth" class="ml-3 text-gray-700">Date of Birth</span>
                    </div>
                    <div class="flex items-center">
                      <i class="fas fa-venus-mars text-blue-600 w-5"></i>
                      <span id="modal-gender" class="ml-3 text-gray-700">Gender</span>
                    </div>
                    <div class="flex items-center">
                      <i class="fas fa-chalkboard-teacher text-blue-600 w-5"></i>
                      <span id="modal-subjects-taught" class="ml-3 text-gray-700">Subjects Taught</span>
                    </div>
                    <div class="flex items-center">
                      <i class="fas fa-users text-blue-600 w-5"></i>
                      <span id="modal-classes-handled" class="ml-3 text-gray-700">Classes Handled</span>
                    </div>
                  </div>
                </div>

                <!-- User Account Information -->
                <div class="mt-4 pt-4 border-t border-gray-200">
                  <h5 class="font-semibold text-gray-700 mb-3 text-sm">Account Information</h5>
                  <div class="space-y-2 text-sm">
                    <div class="flex items-center">
                      <i class="fas fa-user text-blue-600 w-5"></i>
                      <span id="modal-username" class="ml-3 text-gray-700">Username</span>
                    </div>
                    <div class="flex items-center">
                      <i class="fas fa-check-circle text-blue-600 w-5"></i>
                      <span id="modal-account-status" class="ml-3 text-gray-700">Account Status</span>
                    </div>
                    <div class="flex items-center">
                      <i class="fas fa-clock text-blue-600 w-5"></i>
                      <span id="modal-last-login" class="ml-3 text-gray-700">Last Login</span>
                    </div>
                    <div class="flex items-center">
                      <i class="fas fa-calendar-plus text-blue-600 w-5"></i>
                      <span id="modal-account-created" class="ml-3 text-gray-700">Account Created</span>
                    </div>
                  </div>
                </div>

                <!-- Quick Stats -->
                <div class="mt-4 pt-4 border-t border-gray-200">
                  <h5 class="font-semibold text-gray-700 mb-3">Experience Summary</h5>
                  <div class="grid grid-cols-2 gap-4 text-center">
                    <div>
                      <div id="modal-total-experience" class="text-xl font-bold text-blue-600">0</div>
                      <div class="text-xs text-gray-600">Total Years</div>
                    </div>
                    <div>
                      <div id="modal-teaching-experience" class="text-xl font-bold text-blue-600">0</div>
                      <div class="text-xs text-gray-600">Teaching Years</div>
                    </div>
                  </div>
                  <div class="mt-3 text-center">
                    <div>
                      <div id="modal-administrative-experience" class="text-lg font-bold text-blue-600">0</div>
                      <div class="text-xs text-gray-600">Administrative Years</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Timeline Section -->
          <div class="lg:col-span-2">
            <div class="space-y-6">
              <!-- Educational Timeline -->
              <div class="bg-white rounded-lg border shadow-sm overflow-hidden">
                <div class="bg-gradient-to-r from-green-600 to-green-700 text-white p-4">
                  <h3 class="text-lg font-semibold flex items-center">
                    <i class="fas fa-graduation-cap mr-3"></i>
                    Educational Timeline
                  </h3>
                </div>
                <div class="p-4">
                  <div class="relative overflow-x-auto">
                    <div class="flex space-x-6 min-w-max pb-4">
                      <div id="modal-education-timeline" class="flex space-x-6">
                        <!-- Timeline entries will be populated here horizontally -->
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Professional Experience Timeline -->
              <div class="bg-white rounded-lg border shadow-sm overflow-hidden">
                <div class="bg-gradient-to-r from-purple-600 to-purple-700 text-white p-4">
                  <h3 class="text-lg font-semibold flex items-center">
                    <i class="fas fa-briefcase mr-3"></i>
                    Professional Experience Timeline
                  </h3>
                  <p class="text-purple-100 text-sm mt-1">Complete work history including previous organizations and current position</p>
                </div>
                <div class="p-4">
                  <div class="relative overflow-x-auto">
                    <div class="flex space-x-6 min-w-max pb-4">
                      <div id="modal-experience-timeline" class="flex space-x-6">
                        <!-- Timeline entries will be populated here horizontally -->
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Contact Information -->
              <div class="bg-white rounded-lg border shadow-sm overflow-hidden">
                <div class="bg-gradient-to-r from-indigo-600 to-indigo-700 text-white p-4">
                  <h3 class="text-lg font-semibold flex items-center">
                    <i class="fas fa-address-book mr-3"></i>
                    Contact & Administrative Details
                  </h3>
                </div>
                <div class="p-4">
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Contact Information -->
                    <div>
                      <h5 class="font-semibold text-gray-700 mb-3 text-sm">Contact Information</h5>
                      <div class="space-y-2 text-sm">
                        <div class="flex items-center">
                          <i class="fas fa-phone text-indigo-600 w-5"></i>
                          <span id="modal-alternate-phone" class="ml-3 text-gray-700">Alternate Phone</span>
                        </div>
                        <div class="flex items-center">
                          <i class="fas fa-phone-alt text-indigo-600 w-5"></i>
                          <span id="modal-emergency-contact" class="ml-3 text-gray-700">Emergency Contact</span>
                        </div>
                        <div class="flex items-start">
                          <i class="fas fa-map-marker-alt text-indigo-600 w-5 mt-1"></i>
                          <div class="ml-3 text-gray-700">
                            <div id="modal-address" class="mb-1">Address</div>
                            <div class="text-xs text-gray-600">
                              <span id="modal-city">City</span>, <span id="modal-state">State</span> - <span id="modal-pincode">Pincode</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Administrative Details -->
                    <div>
                      <h5 class="font-semibold text-gray-700 mb-3 text-sm">Administrative Details</h5>
                      <div class="space-y-2 text-sm">
                        <div class="flex items-center">
                          <i class="fas fa-building text-indigo-600 w-5"></i>
                          <span id="modal-office-location" class="ml-3 text-gray-700">Office Location</span>
                        </div>
                        <div class="flex items-center">
                          <i class="fas fa-calendar-check text-indigo-600 w-5"></i>
                          <span id="modal-confirmation-date" class="ml-3 text-gray-700">Confirmation Date</span>
                        </div>
                        <div class="flex items-center">
                          <i class="fas fa-arrow-up text-indigo-600 w-5"></i>
                          <span id="modal-last-promotion" class="ml-3 text-gray-700">Last Promotion</span>
                        </div>
                        <div class="flex items-center">
                          <i class="fas fa-star text-indigo-600 w-5"></i>
                          <span id="modal-performance-rating" class="ml-3 text-gray-700">Performance Rating</span>
                        </div>
                        <div class="flex items-center">
                          <i class="fas fa-rupee-sign text-indigo-600 w-5"></i>
                          <span id="modal-current-salary" class="ml-3 text-gray-700">Current Salary</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Publications & Research -->
              <div class="bg-white rounded-lg border shadow-sm overflow-hidden">
                <div class="bg-gradient-to-r from-teal-600 to-teal-700 text-white p-4">
                  <h3 class="text-lg font-semibold flex items-center">
                    <i class="fas fa-book mr-3"></i>
                    Publications & Research
                  </h3>
                </div>
                <div class="p-4">
                  <div class="space-y-4">
                    <div>
                      <h5 class="font-semibold text-gray-700 mb-2 text-sm">Publications</h5>
                      <div id="modal-publications-list" class="text-sm text-gray-600">
                        <!-- Publications will be populated here -->
                      </div>
                    </div>
                    <div>
                      <h5 class="font-semibold text-gray-700 mb-2 text-sm">Research Papers</h5>
                      <div id="modal-research-papers-list" class="text-sm text-gray-600">
                        <!-- Research papers will be populated here -->
                      </div>
                    </div>
                    <div>
                      <h5 class="font-semibold text-gray-700 mb-2 text-sm">Conferences Attended</h5>
                      <div id="modal-conferences-list" class="text-sm text-gray-600">
                        <!-- Conferences will be populated here -->
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Professional Certifications -->
              <div class="bg-white rounded-lg border shadow-sm overflow-hidden">
                <div class="bg-gradient-to-r from-pink-600 to-pink-700 text-white p-4">
                  <h3 class="text-lg font-semibold flex items-center">
                    <i class="fas fa-certificate mr-3"></i>
                    Professional Certifications & Qualifications
                  </h3>
                </div>
                <div class="p-4">
                  <div class="space-y-4">
                    <div>
                      <h5 class="font-semibold text-gray-700 mb-2 text-sm">Professional Certifications</h5>
                      <div id="modal-certifications-list" class="text-sm text-gray-600">
                        <!-- Certifications will be populated here -->
                      </div>
                    </div>
                    <div>
                      <h5 class="font-semibold text-gray-700 mb-2 text-sm">Other Qualifications</h5>
                      <div id="modal-other-qualifications-list" class="text-sm text-gray-600">
                        <!-- Other qualifications will be populated here -->
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Skills and Achievements -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Skills -->
                <div class="bg-white rounded-lg border shadow-sm overflow-hidden">
                  <div class="bg-gradient-to-r from-orange-600 to-orange-700 text-white p-3">
                    <h4 class="text-md font-semibold">Skills & Languages</h4>
                  </div>
                  <div class="p-3">
                    <div class="mb-3">
                      <h5 class="font-semibold text-gray-700 mb-2 text-sm">Special Skills</h5>
                      <div id="modal-skills-list" class="text-sm text-gray-600">
                        <!-- Skills will be populated here -->
                      </div>
                    </div>
                    <div>
                      <h5 class="font-semibold text-gray-700 mb-2 text-sm">Languages Known</h5>
                      <div id="modal-languages-list" class="text-sm text-gray-600">
                        <!-- Languages will be populated here -->
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Achievements -->
                <div class="bg-white rounded-lg border shadow-sm overflow-hidden">
                  <div class="bg-gradient-to-r from-yellow-600 to-yellow-700 text-white p-3">
                    <h4 class="text-md font-semibold">Achievements</h4>
                  </div>
                  <div class="p-3">
                    <div class="mb-3">
                      <h5 class="font-semibold text-gray-700 mb-2 text-sm">Awards Received</h5>
                      <div id="modal-awards-list" class="text-sm text-gray-600 space-y-1">
                        <!-- Awards will be populated here -->
                      </div>
                    </div>
                    <div>
                      <h5 class="font-semibold text-gray-700 mb-2 text-sm">Training Programs</h5>
                      <div id="modal-training-list" class="text-sm text-gray-600 space-y-1">
                        <!-- Training will be populated here -->
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Additional Notes -->
              <div class="bg-white rounded-lg border shadow-sm overflow-hidden">
                <div class="bg-gradient-to-r from-gray-600 to-gray-700 text-white p-4">
                  <h3 class="text-lg font-semibold flex items-center">
                    <i class="fas fa-sticky-note mr-3"></i>
                    Additional Notes
                  </h3>
                </div>
                <div class="p-4">
                  <div id="modal-notes" class="text-sm text-gray-600">
                    <!-- Notes will be populated here -->
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal Footer -->
    <div id="teacherModalFooter" class="bg-gray-50 px-6 py-4 rounded-b-lg flex justify-end space-x-3">
      <button id="closeTeacherModalBtn2" class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600">
        Close
      </button>
      <button id="printTeacherProfile" class="px-4 py-2 bg-principal-primary text-white rounded-md hover:bg-principal-secondary">
        <i class="fas fa-print mr-2"></i>Print Profile
      </button>
    </div>
  </div>
</div>

<!-- All JavaScript functionality has been moved to external files for better organization -->

<script>
$(document).ready(function() {
    console.log('Teacher management page loaded');
    console.log('Enhanced modal functions available:', typeof window.openEnhancedTeacherModal);

    // Add event listeners for view teacher buttons
    $('.view-teacher-btn').on('click', function(e) {
        e.preventDefault();
        const teacherId = $(this).data('teacher-id');
        console.log('View teacher button clicked for ID:', teacherId);

        if (window.openEnhancedTeacherModal) {
            console.log('Calling enhanced modal for teacher:', teacherId);
            window.openEnhancedTeacherModal(teacherId);
        } else {
            console.error('Enhanced modal function not available');
            alert('Enhanced modal not loaded. Please refresh the page.');
        }
    });

    // Add event listeners for modal close buttons
    $('#closeTeacherModalBtn, #closeTeacherModalBtn2').on('click', function() {
        console.log('Close button clicked');
        if (window.closeEnhancedTeacherModal) {
            window.closeEnhancedTeacherModal();
        }
    });

    // Close modal when clicking outside
    $('#teacherModal').on('click', function(e) {
        if (e.target === this) {
            console.log('Clicked outside modal, closing');
            if (window.closeEnhancedTeacherModal) {
                window.closeEnhancedTeacherModal();
            }
        }
    });

    // Print functionality
    $('#printTeacherProfile').on('click', function() {
        console.log('Print button clicked');
        window.print();
    });

    console.log('Event listeners attached');
});

// Search and filter functionality
function refreshTeacherData() {
    console.log('Refreshing teacher data...');
    location.reload();
}

// Search functionality
$(document).on('input', '#search-teachers', function() {
    const searchTerm = $(this).val().toLowerCase();
    $('#teachers-table-body tr').each(function() {
        const name = $(this).data('name') || '';
        const email = $(this).data('email') || '';
        const isVisible = name.includes(searchTerm) || email.includes(searchTerm);
        $(this).toggle(isVisible);
    });
});

// Filter functionality
$(document).on('change', '#filter-performance', function() {
    const filterValue = $(this).val();
    $('#teachers-table-body tr').each(function() {
        const performance = $(this).data('performance') || '';
        const isVisible = !filterValue || performance === filterValue;
        $(this).toggle(isVisible);
    });
});

// Send message functionality
function sendMessage(teacherId) {
    console.log('Send message to teacher:', teacherId);
    alert(`Message functionality for teacher ID: ${teacherId} - To be implemented`);
}
</script>

