<!-- Page-specific styles for teacher management -->
<style>
    .teacher-row {
        transition: all 0.2s ease;
    }
    .teacher-row:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
</style>

<!-- Teacher Management Overview -->
<div class="mb-8">
    <!-- Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-white rounded-lg shadow-md p-6 card-hover">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-principal-light text-principal-primary">
                    <i class="fas fa-users text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Teachers</p>
                    <p class="text-2xl font-bold text-gray-900"><%= teachers.length %></p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6 card-hover">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100 text-green-600">
                    <i class="fas fa-check-circle text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">High Performers</p>
                    <p class="text-2xl font-bold text-gray-900">
                        <%= teachers.filter(t => (t.completion_rate || 0) >= 80).length %>
                    </p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6 card-hover">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                    <i class="fas fa-exclamation-triangle text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Need Attention</p>
                    <p class="text-2xl font-bold text-gray-900">
                        <%= teachers.filter(t => (t.completion_rate || 0) < 60).length %>
                    </p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6 card-hover">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-red-100 text-red-600">
                    <i class="fas fa-clock text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Overdue Tasks</p>
                    <p class="text-2xl font-bold text-gray-900">
                        <%= teachers.reduce((sum, t) => sum + (t.overdue_lectures || 0), 0) %>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Teacher Performance Table -->
<div class="bg-white rounded-lg shadow-md">
    <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h2 class="text-xl font-bold text-gray-900">Teacher Performance Dashboard</h2>
            <div class="flex items-center space-x-4">
                <!-- Export Button -->
                <button class="btn-principal-outline px-4 py-2 rounded-lg text-sm font-medium">
                    <i class="fas fa-download mr-2"></i>
                    Export Report
                </button>
                <!-- Test PDF Button -->
                <button id="testPDFBtn" onclick="
                    console.log('🚨 Button clicked!');
                    try {
                        if (window.jspdf && window.jspdf.jsPDF) {
                            const doc = new window.jspdf.jsPDF();
                            doc.text('Test PDF', 20, 20);
                            doc.save('test.pdf');
                            alert('PDF generated!');
                        } else if (window.jsPDF) {
                            const doc = new window.jsPDF();
                            doc.text('Test PDF', 20, 20);
                            doc.save('test.pdf');
                            alert('PDF generated!');
                        } else {
                            alert('jsPDF not loaded');
                        }
                    } catch(e) {
                        console.error(e);
                        alert('Error: ' + e.message);
                    }
                " class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                    <i class="fas fa-file-pdf mr-2"></i>
                    Test PDF
                </button>
                <!-- Refresh Button -->
                <button onclick="refreshTeacherData()" class="btn-principal px-4 py-2 rounded-lg text-sm font-medium">
                    <i class="fas fa-sync-alt mr-2"></i>
                    Refresh
                </button>
            </div>
        </div>
    </div>

    <div class="p-6">
        <!-- Search and Filter -->
        <div class="mb-6 flex flex-col sm:flex-row gap-4">
            <div class="flex-1">
                <input type="text" id="search-teachers" placeholder="Search teachers by name or email..."
                       class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-principal-primary focus:border-transparent">
            </div>
            <div class="flex gap-2">
                <select id="filter-performance" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-principal-primary focus:border-transparent">
                    <option value="">All Performance</option>
                    <option value="excellent">Excellent (80%+)</option>
                    <option value="good">Good (60-79%)</option>
                    <option value="average">Average (40-59%)</option>
                    <option value="poor">Poor (<40%)</option>
                </select>
            </div>
        </div>

        <% if (teachers && teachers.length > 0) { %>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 table-principal">
                    <thead>
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Teacher
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Lectures
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Completion Rate
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Overdue
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Last Login
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Performance
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200" id="teachers-table-body">
                        <% teachers.forEach(teacher => { %>
                            <%
                                const completionRate = teacher.completion_rate || 0;
                                const performanceLevel = completionRate >= 80 ? 'excellent' : completionRate >= 60 ? 'good' : completionRate >= 40 ? 'average' : 'poor';
                            %>
                            <tr class="hover:bg-gray-50 teacher-row"
                                data-name="<%= teacher.name.toLowerCase() %>"
                                data-email="<%= teacher.email.toLowerCase() %>"
                                data-performance="<%= performanceLevel %>">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            <div class="h-10 w-10 rounded-full bg-principal-light flex items-center justify-center">
                                                <span class="text-sm font-medium text-principal-primary">
                                                    <%= teacher.name.split(' ').map(n => n[0]).join('').toUpperCase() %>
                                                </span>
                                            </div>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">
                                                <%= teacher.name %>
                                            </div>
                                            <div class="text-sm text-gray-500">
                                                <%= teacher.email %>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">
                                        <span class="font-medium"><%= teacher.delivered_lectures || 0 %></span>/<%= teacher.total_lectures || 0 %>
                                    </div>
                                    <div class="text-xs text-gray-500">
                                        Total lectures
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-16 bg-gray-200 rounded-full h-2 mr-3">
                                            <div class="h-2 rounded-full <%= completionRate >= 80 ? 'bg-green-500' : completionRate >= 60 ? 'bg-blue-500' : completionRate >= 40 ? 'bg-yellow-500' : 'bg-red-500' %>"
                                                 style="width: <%= Math.min(completionRate, 100) %>%"></div>
                                        </div>
                                        <span class="text-sm font-medium text-gray-900">
                                            <%= parseFloat(completionRate || 0).toFixed(1) %>%
                                        </span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <% if (teacher.overdue_lectures > 0) { %>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            <i class="fas fa-exclamation-triangle mr-1"></i>
                                            <%= teacher.overdue_lectures %>
                                        </span>
                                    <% } else { %>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            <i class="fas fa-check mr-1"></i>
                                            Up to date
                                        </span>
                                    <% } %>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <% if (teacher.last_login) { %>
                                        <%= new Date(teacher.last_login).toLocaleDateString() %>
                                    <% } else { %>
                                        Never
                                    <% } %>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= completionRate >= 80 ? 'bg-green-100 text-green-800' : completionRate >= 60 ? 'bg-blue-100 text-blue-800' : completionRate >= 40 ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800' %>">
                                        <% if (completionRate >= 80) { %>
                                            <i class="fas fa-star mr-1"></i> Excellent
                                        <% } else if (completionRate >= 60) { %>
                                            <i class="fas fa-thumbs-up mr-1"></i> Good
                                        <% } else if (completionRate >= 40) { %>
                                            <i class="fas fa-minus-circle mr-1"></i> Average
                                        <% } else { %>
                                            <i class="fas fa-times-circle mr-1"></i> Poor
                                        <% } %>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <button id="viewTeacherBtn-<%= teacher.id %>"
                                                class="view-teacher-btn text-principal-primary hover:text-principal-dark p-1 rounded"
                                                data-teacher-id="<%= teacher.id %>"
                                                title="View Teacher Details">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button onclick="sendMessage(<%= teacher.id %>)"
                                                class="text-blue-600 hover:text-blue-900 p-1 rounded"
                                                title="Send Message">
                                            <i class="fas fa-envelope"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <% }); %>
                    </tbody>
                </table>
            </div>
        <% } else { %>
            <div class="text-center py-12">
                <i class="fas fa-user-friends text-4xl text-gray-300 mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900">No teachers found</h3>
                <p class="text-sm text-gray-500">Teacher data will appear here once teachers are added to the system.</p>
            </div>
        <% } %>
    </div>
</div>

<!-- Enhanced Teacher Details Modal -->
<div id="teacherModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
  <div class="bg-white rounded-lg shadow-xl max-w-[95vw] w-full max-h-[95vh] overflow-y-auto mx-2">
    <!-- Modal Header -->
    <div class="bg-gradient-to-r from-principal-primary to-principal-secondary text-white px-6 py-4 rounded-t-lg flex justify-between items-center">
      <h3 id="teacherModalTitle" class="text-xl font-semibold flex items-center">
        <i class="fas fa-user-tie mr-3"></i>
        Enhanced Teacher Profile
      </h3>
      <button id="closeTeacherModalBtn" class="text-white hover:text-gray-200">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>

    <!-- Modal Content -->
    <div id="teacherModalContent" class="p-6">
      <!-- Loading state -->
      <div id="modal-loading" class="flex items-center justify-center py-12">
        <i class="fas fa-spinner fa-spin text-3xl text-principal-primary mr-3"></i>
        <span class="text-lg text-gray-600">Loading teacher profile...</span>
      </div>

      <!-- Enhanced Profile Content (will be populated) -->
      <div id="enhanced-profile-content" class="hidden">
        <!-- Personal Information Section - Full Width at Top -->
        <div class="bg-white rounded-lg border shadow-sm overflow-hidden mb-6">
          <div class="bg-gradient-to-r from-blue-600 to-blue-700 text-white p-4">
            <h3 class="text-xl font-semibold flex items-center">
              <i class="fas fa-user-tie mr-3"></i>
              Personal Information
            </h3>
          </div>
          <div class="p-6">
            <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
              <!-- Profile Image and Basic Info -->
              <div class="lg:col-span-1">
                <div class="flex flex-col items-center">
                  <div class="relative mb-4">
                    <div id="modal-profile-image-container" class="w-32 h-32 rounded-full bg-blue-600 border-4 border-blue-600 shadow-lg overflow-hidden flex items-center justify-center">
                      <div id="modal-profile-image-placeholder" class="text-3xl font-bold text-white">T</div>
                      <img id="modal-profile-image" class="w-full h-full object-cover hidden" src="" alt="Profile Image">
                    </div>
                  </div>
                  <h4 id="modal-teacher-name" class="text-xl font-bold text-gray-800 text-center mb-2">Loading...</h4>
                  <p id="modal-teacher-designation" class="text-blue-600 font-semibold text-center mb-2">Teacher</p>
                  <span id="modal-teacher-department" class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
                    Academic Department
                  </span>
                </div>
              </div>

              <!-- Basic Information -->
              <div class="lg:col-span-1">
                <h5 class="font-semibold text-gray-700 mb-4 text-sm uppercase tracking-wide">Basic Information</h5>
                <div class="space-y-3 text-sm">
                  <div class="flex items-center">
                    <i class="fas fa-id-badge text-blue-600 w-5"></i>
                    <span id="modal-teacher-employee-id" class="ml-3 text-gray-700">EMP0001</span>
                  </div>
                  <div class="flex items-center">
                    <i class="fas fa-envelope text-blue-600 w-5"></i>
                    <span id="modal-teacher-email" class="ml-3 text-gray-700"><EMAIL></span>
                  </div>
                  <div class="flex items-center">
                    <i class="fas fa-phone text-blue-600 w-5"></i>
                    <span id="modal-teacher-phone" class="ml-3 text-gray-700">+91-XXXXXXXXXX</span>
                  </div>
                  <div class="flex items-center">
                    <i class="fas fa-calendar text-blue-600 w-5"></i>
                    <span id="modal-teacher-joining-date" class="ml-3 text-gray-700">Joining Date</span>
                  </div>
                  <div class="flex items-center">
                    <i class="fas fa-briefcase text-blue-600 w-5"></i>
                    <span id="modal-teacher-employment-type" class="ml-3 text-gray-700">Employment Type</span>
                  </div>
                </div>
              </div>

              <!-- Personal Details -->
              <div class="lg:col-span-1">
                <h5 class="font-semibold text-gray-700 mb-4 text-sm uppercase tracking-wide">Personal Details</h5>
                <div class="space-y-3 text-sm">
                  <div class="flex items-center">
                    <i class="fas fa-birthday-cake text-blue-600 w-5"></i>
                    <span id="modal-date-of-birth" class="ml-3 text-gray-700">Date of Birth</span>
                  </div>
                  <div class="flex items-center">
                    <i class="fas fa-venus-mars text-blue-600 w-5"></i>
                    <span id="modal-gender" class="ml-3 text-gray-700">Gender</span>
                  </div>
                  <div class="flex items-center">
                    <i class="fas fa-chalkboard-teacher text-blue-600 w-5"></i>
                    <span id="modal-subjects-taught" class="ml-3 text-gray-700">Subjects Taught</span>
                  </div>
                  <div class="flex items-center">
                    <i class="fas fa-users text-blue-600 w-5"></i>
                    <span id="modal-classes-handled" class="ml-3 text-gray-700">Classes Handled</span>
                  </div>
                  <div class="flex items-center">
                    <i class="fas fa-user text-blue-600 w-5"></i>
                    <span id="modal-username" class="ml-3 text-gray-700">Username</span>
                  </div>
                </div>
              </div>

              <!-- Account & Experience Summary -->
              <div class="lg:col-span-1">
                <h5 class="font-semibold text-gray-700 mb-4 text-sm uppercase tracking-wide">Account & Experience</h5>
                <div class="space-y-3 text-sm mb-4">
                  <div class="flex items-center">
                    <i class="fas fa-check-circle text-blue-600 w-5"></i>
                    <span id="modal-account-status" class="ml-3 text-gray-700">Account Status</span>
                  </div>
                  <div class="flex items-center">
                    <i class="fas fa-clock text-blue-600 w-5"></i>
                    <span id="modal-last-login" class="ml-3 text-gray-700">Last Login</span>
                  </div>
                  <div class="flex items-center">
                    <i class="fas fa-calendar-plus text-blue-600 w-5"></i>
                    <span id="modal-account-created" class="ml-3 text-gray-700">Account Created</span>
                  </div>
                </div>

                <!-- Experience Stats -->
                <div class="bg-blue-50 rounded-lg p-3">
                  <h6 class="font-semibold text-blue-800 mb-2 text-xs uppercase tracking-wide">Experience Summary</h6>
                  <div class="grid grid-cols-2 gap-2 text-center">
                    <div>
                      <div id="modal-total-experience" class="text-lg font-bold text-blue-600">0</div>
                      <div class="text-xs text-blue-600">Total Years</div>
                    </div>
                    <div>
                      <div id="modal-teaching-experience" class="text-lg font-bold text-blue-600">0</div>
                      <div class="text-xs text-blue-600">Teaching Years</div>
                    </div>
                  </div>
                  <div class="mt-2 text-center">
                    <div id="modal-administrative-experience" class="text-sm font-bold text-blue-600">0</div>
                    <div class="text-xs text-blue-600">Admin Years</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Main Content Grid -->
        <div class="space-y-4">
          <!-- Educational Timeline - Full Width -->
          <div class="bg-white rounded-lg border shadow-sm overflow-hidden">
            <div class="bg-gradient-to-r from-green-600 to-green-700 text-white p-3">
              <h3 class="text-lg font-semibold flex items-center">
                <i class="fas fa-graduation-cap mr-3"></i>
                Educational Timeline
              </h3>
            </div>
            <div class="p-3">
              <div class="relative overflow-x-auto">
                <div class="flex space-x-4 min-w-max pb-2">
                  <div id="modal-education-timeline" class="flex space-x-4">
                    <!-- Timeline entries will be populated here horizontally -->
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Professional Experience Timeline - Full Width -->
          <div class="bg-white rounded-lg border shadow-sm overflow-hidden">
            <div class="bg-gradient-to-r from-purple-600 to-purple-700 text-white p-3">
              <h3 class="text-lg font-semibold flex items-center">
                <i class="fas fa-briefcase mr-3"></i>
                Professional Experience Timeline
              </h3>
              <p class="text-purple-100 text-sm mt-1">Complete work history including previous organizations and current position</p>
            </div>
            <div class="p-3">
              <div class="relative overflow-x-auto">
                <div class="flex space-x-4 min-w-max pb-2">
                  <div id="modal-experience-timeline" class="flex space-x-4">
                    <!-- Timeline entries will be populated here horizontally -->
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Two Column Grid for Contact/Admin and Publications/Certifications -->
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <!-- Contact & Administrative Details -->
            <div class="bg-white rounded-lg border shadow-sm overflow-hidden">
              <div class="bg-gradient-to-r from-indigo-600 to-indigo-700 text-white p-3">
                <h3 class="text-lg font-semibold flex items-center">
                  <i class="fas fa-address-book mr-3"></i>
                  Contact & Administrative
                </h3>
              </div>
              <div class="p-3">
                <div class="space-y-4">
                  <!-- Contact Information -->
                  <div>
                    <h5 class="font-semibold text-gray-700 mb-2 text-sm">Contact Information</h5>
                    <div class="space-y-1 text-sm">
                      <div class="flex items-center">
                        <i class="fas fa-phone text-indigo-600 w-4"></i>
                        <span id="modal-alternate-phone" class="ml-2 text-gray-700">Alternate Phone</span>
                      </div>
                      <div class="flex items-center">
                        <i class="fas fa-phone-alt text-indigo-600 w-4"></i>
                        <span id="modal-emergency-contact" class="ml-2 text-gray-700">Emergency Contact</span>
                      </div>
                      <div class="flex items-start">
                        <i class="fas fa-map-marker-alt text-indigo-600 w-4 mt-1"></i>
                        <div class="ml-2 text-gray-700">
                          <div id="modal-address" class="mb-1">Address</div>
                          <div class="text-xs text-gray-600">
                            <span id="modal-city">City</span>, <span id="modal-state">State</span> - <span id="modal-pincode">Pincode</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Administrative Details -->
                  <div>
                    <h5 class="font-semibold text-gray-700 mb-2 text-sm">Administrative Details</h5>
                    <div class="space-y-1 text-sm">
                      <div class="flex items-center">
                        <i class="fas fa-building text-indigo-600 w-4"></i>
                        <span id="modal-office-location" class="ml-2 text-gray-700">Office Location</span>
                      </div>
                      <div class="flex items-center">
                        <i class="fas fa-calendar-check text-indigo-600 w-4"></i>
                        <span id="modal-confirmation-date" class="ml-2 text-gray-700">Confirmation Date</span>
                      </div>
                      <div class="flex items-center">
                        <i class="fas fa-arrow-up text-indigo-600 w-4"></i>
                        <span id="modal-last-promotion" class="ml-2 text-gray-700">Last Promotion</span>
                      </div>
                      <div class="flex items-center">
                        <i class="fas fa-star text-indigo-600 w-4"></i>
                        <span id="modal-performance-rating" class="ml-2 text-gray-700">Performance Rating</span>
                      </div>
                      <div class="flex items-center">
                        <i class="fas fa-rupee-sign text-indigo-600 w-4"></i>
                        <span id="modal-current-salary" class="ml-2 text-gray-700">Current Salary</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Publications & Research -->
            <div class="bg-white rounded-lg border shadow-sm overflow-hidden">
              <div class="bg-gradient-to-r from-teal-600 to-teal-700 text-white p-3">
                <h3 class="text-lg font-semibold flex items-center">
                  <i class="fas fa-book mr-3"></i>
                  Publications & Research
                </h3>
              </div>
              <div class="p-3">
                <div class="space-y-3">
                  <div>
                    <h5 class="font-semibold text-gray-700 mb-2 text-sm">Publications</h5>
                    <div id="modal-publications-list" class="text-sm text-gray-600">
                      <!-- Publications will be populated here -->
                    </div>
                  </div>
                  <div>
                    <h5 class="font-semibold text-gray-700 mb-2 text-sm">Research Papers</h5>
                    <div id="modal-research-papers-list" class="text-sm text-gray-600">
                      <!-- Research papers will be populated here -->
                    </div>
                  </div>
                  <div>
                    <h5 class="font-semibold text-gray-700 mb-2 text-sm">Conferences</h5>
                    <div id="modal-conferences-list" class="text-sm text-gray-600">
                      <!-- Conferences will be populated here -->
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Two Column Grid for Certifications and Skills -->
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <!-- Professional Certifications -->
            <div class="bg-white rounded-lg border shadow-sm overflow-hidden">
              <div class="bg-gradient-to-r from-pink-600 to-pink-700 text-white p-3">
                <h3 class="text-lg font-semibold flex items-center">
                  <i class="fas fa-certificate mr-3"></i>
                  Certifications & Qualifications
                </h3>
              </div>
              <div class="p-3">
                <div class="space-y-3">
                  <div>
                    <h5 class="font-semibold text-gray-700 mb-2 text-sm">Professional Certifications</h5>
                    <div id="modal-certifications-list" class="text-sm text-gray-600">
                      <!-- Certifications will be populated here -->
                    </div>
                  </div>
                  <div>
                    <h5 class="font-semibold text-gray-700 mb-2 text-sm">Other Qualifications</h5>
                    <div id="modal-other-qualifications-list" class="text-sm text-gray-600">
                      <!-- Other qualifications will be populated here -->
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Skills Section -->
            <div class="bg-white rounded-lg border shadow-sm overflow-hidden">
              <div class="bg-gradient-to-r from-orange-600 to-orange-700 text-white p-3">
                <h3 class="text-lg font-semibold flex items-center">
                  <i class="fas fa-cogs mr-3"></i>
                  Skills & Languages
                </h3>
              </div>
              <div class="p-3">
                <div class="space-y-3">
                  <div>
                    <h5 class="font-semibold text-gray-700 mb-2 text-sm">Special Skills</h5>
                    <div id="modal-skills-list" class="text-sm text-gray-600">
                      <!-- Skills will be populated here -->
                    </div>
                  </div>
                  <div>
                    <h5 class="font-semibold text-gray-700 mb-2 text-sm">Languages Known</h5>
                    <div id="modal-languages-list" class="text-sm text-gray-600">
                      <!-- Languages will be populated here -->
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Comprehensive Achievements Section - Full Width -->
          <div class="bg-white rounded-lg border shadow-sm overflow-hidden">
            <div class="bg-gradient-to-r from-yellow-600 to-yellow-700 text-white p-3">
              <h3 class="text-lg font-semibold flex items-center">
                <i class="fas fa-trophy mr-3"></i>
                Achievements & Recognition
              </h3>
              <p class="text-yellow-100 text-sm mt-1">Professional accomplishments and awards</p>
            </div>
            <div class="p-3">
              <div id="modal-achievements-content">
                <!-- Achievements will be populated here -->
              </div>
            </div>
          </div>

          <!-- Additional Notes - Full Width -->
          <div class="bg-white rounded-lg border shadow-sm overflow-hidden">
            <div class="bg-gradient-to-r from-gray-600 to-gray-700 text-white p-3">
              <h3 class="text-lg font-semibold flex items-center">
                <i class="fas fa-sticky-note mr-3"></i>
                Additional Notes
              </h3>
            </div>
            <div class="p-3">
              <div id="modal-notes" class="text-sm text-gray-600">
                <!-- Notes will be populated here -->
              </div>
            </div>
          </div>
        </div>
          </div>
        </div>

        <!-- Modal Footer - Moved inside content area -->
        <div id="teacherModalFooter" class="bg-gray-50 px-6 py-4 mt-6 rounded-lg flex justify-end space-x-3 border-t border-gray-200">
          <button id="closeTeacherModalBtn2" class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600">
            Close
          </button>
          <button id="printTeacherProfile" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors">
            <i class="fas fa-file-pdf mr-2"></i>Generate CV PDF
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Custom CSS for compact layout -->
<style>
.line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Compact modal spacing */
.enhanced-achievements-section .achievement-category {
    margin-bottom: 1rem;
}

/* Responsive grid improvements */
@media (min-width: 1024px) {
    .achievement-category .grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }
}

/* Smooth transitions */
.achievement-category > div {
    transition: all 0.2s ease-in-out;
}

/* Compact timeline spacing */
.flex.space-x-4 {
    gap: 1rem;
}

.flex.space-x-6 {
    gap: 1.5rem;
}

/* Modal content optimization */
#enhanced-profile-content .space-y-4 > * + * {
    margin-top: 1rem;
}

#enhanced-profile-content .space-y-6 > * + * {
    margin-top: 1.5rem;
}

/* Ensure full width utilization */
.modal-content-container {
    max-width: none;
    width: 95vw;
}

/* Modal footer styling */
#teacherModalFooter {
    margin-top: 1.5rem;
    background-color: #f9fafb;
    border-top: 1px solid #e5e7eb;
}

/* Make sure PDF button is visible and styled */
#printTeacherProfile {
    min-width: 150px;
    font-weight: 500;
    display: flex !important;
    align-items: center;
    justify-content: center;
    background-color: #2563eb !important;
    color: white !important;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 20;
}

#printTeacherProfile:hover {
    background-color: #1d4ed8 !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Ensure modal footer is always visible */
#teacherModalFooter {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .modal-content-container {
        width: 98vw;
        margin: 0.5rem;
    }

    .achievement-category .grid {
        grid-template-columns: 1fr;
    }
}
</style>

<!-- All JavaScript functionality has been moved to external files for better organization -->

<!-- jsPDF Library for PDF generation -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
<script src="/js/enhanced-teacher-modal-display.js"></script>
<script src="/js/enhanced-teacher-modal.js"></script>
<script src="/js/teacher-profile-pdf-generator.js"></script>

<!-- Simple test PDF function - defined globally -->
<script>
// Simple test PDF function - GLOBAL SCOPE
function testPDFClick() {
    console.log('🚨 INLINE ONCLICK TRIGGERED!');
    alert('Inline onclick works! Now testing PDF...');

    try {
        // Check if jsPDF is available
        console.log('Checking jsPDF...');
        console.log('window.jspdf:', typeof window.jspdf);
        console.log('window.jsPDF:', typeof window.jsPDF);

        let jsPDFClass;
        if (window.jspdf && window.jspdf.jsPDF) {
            jsPDFClass = window.jspdf.jsPDF;
            console.log('Using window.jspdf.jsPDF');
        } else if (window.jsPDF) {
            jsPDFClass = window.jsPDF;
            console.log('Using window.jsPDF');
        } else {
            alert('jsPDF library not found!');
            return;
        }

        // Create simple PDF
        console.log('Creating PDF...');
        const doc = new jsPDFClass();
        doc.text('Test PDF from inline function', 20, 20);
        doc.text('Date: ' + new Date().toLocaleString(), 20, 40);
        doc.save('inline-test.pdf');

        console.log('✅ PDF created successfully!');
        alert('PDF generated successfully!');
    } catch (error) {
        console.error('❌ Error:', error);
        alert('Error: ' + error.message);
    }
}

$(document).ready(function() {
    console.log('Teacher management page loaded');
    console.log('Enhanced modal functions available:', typeof window.openEnhancedTeacherModal);

    // Debug button existence
    console.log('🔍 Checking buttons on page load:');
    console.log('Test PDF button exists:', $('#testPDFBtn').length);
    console.log('Test PDF button HTML:', $('#testPDFBtn').html());

    // Add immediate click handler for testing
    $('#testPDFBtn').click(function() {
        console.log('🚨 JQUERY CLICK HANDLER TRIGGERED!');

        try {
            if (window.jspdf && window.jspdf.jsPDF) {
                const doc = new window.jspdf.jsPDF();
                doc.text('jQuery Test PDF', 20, 20);
                doc.text('Generated at: ' + new Date().toLocaleString(), 20, 40);
                doc.save('jquery-test.pdf');
                alert('jQuery PDF generated!');
            } else if (window.jsPDF) {
                const doc = new window.jsPDF();
                doc.text('jQuery Test PDF', 20, 20);
                doc.text('Generated at: ' + new Date().toLocaleString(), 20, 40);
                doc.save('jquery-test.pdf');
                alert('jQuery PDF generated!');
            } else {
                alert('jsPDF not loaded in jQuery handler');
            }
        } catch(e) {
            console.error('jQuery PDF error:', e);
            alert('jQuery Error: ' + e.message);
        }
    });

    // Test if jQuery is working at all
    console.log('jQuery version:', $.fn.jquery);
    console.log('Document ready fired');

    // Add a simple test button click
    setTimeout(function() {
        console.log('🔍 Delayed check - Test PDF button exists:', $('#testPDFBtn').length);
        if ($('#testPDFBtn').length > 0) {
            console.log('✅ Test PDF button found after delay');
        } else {
            console.log('❌ Test PDF button NOT found after delay');
        }
    }, 1000);

    // Add event listeners for view teacher buttons
    $('.view-teacher-btn').on('click', function(e) {
        e.preventDefault();
        const teacherId = $(this).data('teacher-id');
        console.log('View teacher button clicked for ID:', teacherId);

        if (window.openEnhancedTeacherModal) {
            console.log('Calling enhanced modal for teacher:', teacherId);
            window.openEnhancedTeacherModal(teacherId);
        } else {
            console.error('Enhanced modal function not available');
            alert('Enhanced modal not loaded. Please refresh the page.');
        }
    });

    // Add event listeners for modal close buttons
    $('#closeTeacherModalBtn, #closeTeacherModalBtn2').on('click', function() {
        console.log('Close button clicked');
        if (window.closeEnhancedTeacherModal) {
            window.closeEnhancedTeacherModal();
        }
    });

    // Close modal when clicking outside
    $('#teacherModal').on('click', function(e) {
        if (e.target === this) {
            console.log('Clicked outside modal, closing');
            if (window.closeEnhancedTeacherModal) {
                window.closeEnhancedTeacherModal();
            }
        }
    });

    // Check if PDF button exists
    console.log('PDF button exists:', $('#printTeacherProfile').length);
    console.log('Modal footer exists:', $('#teacherModalFooter').length);

    // PDF Generation functionality using event delegation
    $(document).on('click', '#printTeacherProfile', function() {
        console.log('PDF generation button clicked');

        // Check if teacher data is available
        if (!window.currentTeacherData) {
            alert('No teacher data available. Please open a teacher profile first.');
            return;
        }

        // Check if PDF generator is loaded
        if (typeof window.generateTeacherProfilePDF !== 'function') {
            alert('PDF generator not loaded. Please refresh the page and try again.');
            return;
        }

        // Check if jsPDF is available
        if (typeof window.jspdf === 'undefined' && typeof window.jsPDF === 'undefined') {
            alert('PDF library not loaded. Please refresh the page and try again.');
            return;
        }

        // Show loading state
        const button = $(this);
        const originalText = button.html();
        button.html('<i class="fas fa-spinner fa-spin mr-2"></i>Generating PDF...');
        button.prop('disabled', true);

        // Generate PDF
        setTimeout(() => {
            try {
                const success = window.generateTeacherProfilePDF(window.currentTeacherData);

                if (success) {
                    // Show success message
                    button.html('<i class="fas fa-check mr-2"></i>PDF Generated!');
                    setTimeout(() => {
                        button.html(originalText);
                        button.prop('disabled', false);
                    }, 2000);
                } else {
                    throw new Error('PDF generation failed');
                }
            } catch (error) {
                console.error('PDF generation error:', error);
                alert('Failed to generate PDF. Please try again.');
                button.html(originalText);
                button.prop('disabled', false);
            }
        }, 100);
    });

    // Test PDF button functionality using event delegation
    $(document).on('click', '#testPDFBtn', function() {
        console.log('🔄 Test PDF button clicked');

        // Show loading state
        const button = $(this);
        const originalText = button.html();
        button.html('<i class="fas fa-spinner fa-spin mr-2"></i>Testing PDF...');
        button.prop('disabled', true);

        // Check library availability first
        console.log('🔍 Checking PDF libraries...');
        console.log('jsPDF available:', typeof window.jspdf);
        console.log('jsPDF global:', typeof window.jsPDF);
        console.log('PDF generator function:', typeof window.generateTeacherProfilePDF);

        // Create sample teacher data for testing
        const sampleTeacher = {
            name: 'Test Teacher',
            designation: 'Computer Science Teacher',
            department: 'Computer Science',
            employee_id: 'TEST001',
            email: '<EMAIL>',
            phone: '+91-9876543210',
            joining_date: '2020-01-15',
            employment_type: 'Permanent',
            date_of_birth: '1985-05-20',
            gender: 'Male',
            subjects_taught: 'Computer Science, Programming',
            total_experience_years: 8,
            teaching_experience_years: 6,
            address: '123 Test Street',
            city: 'Test City',
            state: 'Test State',
            pincode: '123456'
        };

        setTimeout(() => {
            try {
                // Check if PDF generator is available
                if (typeof window.generateTeacherProfilePDF !== 'function') {
                    throw new Error('PDF generator function not loaded');
                }

                // Check if jsPDF is available
                if (typeof window.jspdf === 'undefined' && typeof window.jsPDF === 'undefined') {
                    throw new Error('jsPDF library not loaded');
                }

                console.log('✅ All checks passed, generating PDF...');

                // Try simple jsPDF test first
                try {
                    let jsPDFClass;
                    if (window.jspdf && window.jspdf.jsPDF) {
                        jsPDFClass = window.jspdf.jsPDF;
                    } else if (window.jsPDF) {
                        jsPDFClass = window.jsPDF;
                    } else {
                        throw new Error('jsPDF not available');
                    }

                    console.log('🔄 Creating simple test PDF...');
                    const doc = new jsPDFClass();
                    doc.text('Test PDF Generation', 20, 20);
                    doc.text('Teacher: ' + sampleTeacher.name, 20, 40);
                    doc.text('Department: ' + sampleTeacher.department, 20, 60);
                    doc.save('test-pdf.pdf');
                    console.log('✅ Simple PDF test successful');

                    // Now try full PDF generation
                    const success = window.generateTeacherProfilePDF(sampleTeacher);

                    if (success) {
                        button.html('<i class="fas fa-check mr-2"></i>PDF Generated!');
                        setTimeout(() => {
                            button.html(originalText);
                            button.prop('disabled', false);
                        }, 2000);
                    } else {
                        throw new Error('PDF generation returned false');
                    }
                } catch (simpleError) {
                    console.error('❌ Simple PDF test failed:', simpleError);
                    // Try the full generator anyway
                    const success = window.generateTeacherProfilePDF(sampleTeacher);

                    if (success) {
                        button.html('<i class="fas fa-check mr-2"></i>PDF Generated!');
                        setTimeout(() => {
                            button.html(originalText);
                            button.prop('disabled', false);
                        }, 2000);
                    } else {
                        throw new Error('PDF generation returned false');
                    }
                }
            } catch (error) {
                console.error('❌ PDF generation error:', error);
                alert(`PDF generation failed: ${error.message}`);
                button.html(originalText);
                button.prop('disabled', false);
            }
        }, 100);
    });

    console.log('Event listeners attached');
});

// Simple PDF generation function that can be called directly
window.generateSimplePDF = function() {
    console.log('🔄 Direct PDF generation called');

    try {
        // Check if jsPDF is available
        let jsPDFClass;
        if (window.jspdf && window.jspdf.jsPDF) {
            jsPDFClass = window.jspdf.jsPDF;
        } else if (window.jsPDF) {
            jsPDFClass = window.jsPDF;
        } else {
            alert('jsPDF library not loaded');
            return false;
        }

        // Create simple PDF
        const doc = new jsPDFClass();
        doc.text('Simple PDF Test', 20, 20);
        doc.text('Generated at: ' + new Date().toLocaleString(), 20, 40);
        doc.text('This confirms PDF generation is working!', 20, 60);
        doc.save('simple-test.pdf');

        console.log('✅ Simple PDF generated successfully');
        return true;
    } catch (error) {
        console.error('❌ Simple PDF generation failed:', error);
        alert('PDF generation failed: ' + error.message);
        return false;
    }
};

// Function to generate teacher PDF directly
window.generateTeacherPDF = function() {
    console.log('🔄 Direct teacher PDF generation called');

    if (!window.currentTeacherData) {
        alert('No teacher data available. Please open a teacher profile first.');
        return false;
    }

    if (typeof window.generateTeacherProfilePDF === 'function') {
        return window.generateTeacherProfilePDF(window.currentTeacherData);
    } else {
        alert('PDF generator not loaded');
        return false;
    }
};

// Search and filter functionality
function refreshTeacherData() {
    console.log('Refreshing teacher data...');
    location.reload();
}

// Search functionality
$(document).on('input', '#search-teachers', function() {
    const searchTerm = $(this).val().toLowerCase();
    $('#teachers-table-body tr').each(function() {
        const name = $(this).data('name') || '';
        const email = $(this).data('email') || '';
        const isVisible = name.includes(searchTerm) || email.includes(searchTerm);
        $(this).toggle(isVisible);
    });
});

// Filter functionality
$(document).on('change', '#filter-performance', function() {
    const filterValue = $(this).val();
    $('#teachers-table-body tr').each(function() {
        const performance = $(this).data('performance') || '';
        const isVisible = !filterValue || performance === filterValue;
        $(this).toggle(isVisible);
    });
});

// Send message functionality
function sendMessage(teacherId) {
    console.log('Send message to teacher:', teacherId);
    alert(`Message functionality for teacher ID: ${teacherId} - To be implemented`);
}

// Duplicate function removed - using global scope version above
</script>

